<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        // Modify enum for lead_source (remove upwork variants)
        DB::statement("ALTER TABLE prospects MODIFY lead_source ENUM(
            'linkedin',
            'upwork_jignesh',
            'upwork_abhishek',
            'email',
            'referral',
            'website',
            'cold_call',
            'social_media',
            'other'
        ) DEFAULT 'other'");

        // Drop unique constraint on email
        Schema::table('prospects', function (Blueprint $table) {
            $table->dropUnique('prospects_email_unique');
        });

        // Modify other columns to be nullable
        Schema::table('prospects', function (Blueprint $table) {
            $table->string('last_name')->nullable()->change();
            $table->string('email')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE prospects MODIFY lead_source ENUM(
            'linkedin',
            'upwork',
            'email',
            'referral',
            'website',
            'cold_call',
            'social_media',
            'other'
        ) DEFAULT 'other'");

        // Re-add unique on email
        Schema::table('prospects', function (Blueprint $table) {
            $table->unique('email');
        });
    }
};
