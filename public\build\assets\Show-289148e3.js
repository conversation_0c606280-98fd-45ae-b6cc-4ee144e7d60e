import{r as U,T as k,o as i,c as d,a,u as n,w as m,F as S,Z as Q,b as t,t as l,f as c,d as A,g as p,n as M,h as V,k as x,v as D,y as $,G as Y}from"./app-b1cf8b3a.js";import{_ as Z,b as j}from"./AdminLayout-13d09996.js";import{P as C}from"./PrimaryButton-05be7944.js";import{_ as g}from"./SecondaryButton-5ec31741.js";import{M as F}from"./Modal-9f7e750d.js";import{_ as f}from"./InputLabel-352dc786.js";import{_ as N}from"./TextInput-796570c1.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const H={class:"animate-top"},J={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},K={class:"text-3xl font-bold text-gray-900"},X=t("p",{class:"text-gray-600 mt-1"},"Prospect Details",-1),tt={class:"flex space-x-4"},et=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),st={class:"grid grid-cols-1 lg:grid-cols-9 gap-6"},ot={class:"lg:col-span-6 space-y-6"},lt={class:"bg-white shadow rounded-lg p-6"},at=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1),it={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1),nt={class:"mt-1 text-sm text-gray-700"},ct=t("label",{class:"block text-sm font-semibold text-gray-900"},"Email",-1),rt={class:"mt-1 text-sm text-gray-700"},ut={key:0},mt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Phone",-1),pt={class:"mt-1 text-sm text-gray-700"},ft={key:1},_t=t("label",{class:"block text-sm font-semibold text-gray-900"},"Company",-1),bt={class:"mt-1 text-sm text-gray-700"},xt={key:2},gt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Position",-1),ht={class:"mt-1 text-sm text-gray-700"},yt={key:3},vt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Location",-1),wt={class:"mt-1 text-sm text-gray-700"},kt={class:"bg-white shadow rounded-lg p-6"},St=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Activity Timeline",-1),Ct={key:0,class:"space-y-4"},Ut={class:"flex-shrink-0"},At={class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},Vt={class:"text-indigo-600 text-xs font-medium"},$t={class:"flex-1 min-w-0"},Ft={class:"flex items-center justify-between"},Nt={class:"text-sm font-semibold text-gray-900"},Pt={key:0,class:"mt-1 text-sm text-gray-500 whitespace-pre-line"},Mt={class:"flex items-center justify-between"},Dt={key:0,class:"mt-1 text-xs text-gray-600"},jt={class:"text-xs text-gray-600"},qt={key:1,class:"text-center py-8 text-gray-500"},Bt={class:"lg:col-span-3 space-y-6"},Lt={class:"bg-white shadow rounded-lg p-6"},Tt=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1),It={class:"flex flex-wrap gap-3"},Et={key:0,class:"bg-white shadow rounded-lg p-6"},Ot=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Notes & Conversation",-1),Rt={class:"space-y-4"},Wt={key:0},zt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Initial Conversation:",-1),Gt={class:"mt-1 text-sm text-gray-700 whitespace-pre-line"},Qt={key:1},Yt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Requirements:",-1),Zt={class:"mt-1 text-sm text-gray-700 whitespace-pre-line"},Ht={key:2},Jt=t("label",{class:"block text-sm font-semibold text-gray-900"},"Notes:",-1),Kt={class:"mt-1 text-sm text-gray-700 whitespace-pre-line"},Xt={key:1,class:"bg-white shadow rounded-lg p-6"},te=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"URLs",-1),ee={class:"flex flex-col gap-1"},se=["href"],oe=["href"],le=["href"],ae={class:"bg-white shadow rounded-lg p-6"},ie=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Status & Priority",-1),de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ne={class:""},ce=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status:",-1),re={class:""},ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority:",-1),me=t("label",{class:"block text-sm font-semibold text-gray-900"},"Score:",-1),pe={class:"mt-1 text-sm text-gray-700"},fe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Source:",-1),_e={class:"mt-1 text-sm text-gray-700"},be={key:0},xe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To:",-1),ge={class:"mt-1 text-sm text-gray-700"},he={class:"bg-white shadow rounded-lg p-6"},ye=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Project Information",-1),ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},we={key:0},ke=t("label",{class:"block text-sm font-semibold text-gray-900"},"Project Type:",-1),Se={class:"mt-1 text-sm text-gray-700"},Ce={key:1},Ue=t("label",{class:"block text-sm font-semibold text-gray-900"},"Estimated Budget:",-1),Ae={class:"mt-1 text-sm text-gray-700"},Ve={key:2},$e=t("label",{class:"block text-sm font-semibold text-gray-900"},"Next Follow-up:",-1),Fe={class:"mt-1 text-sm text-gray-700"},Ne={key:3},Pe=t("label",{class:"block text-sm font-semibold text-gray-900"},"Converted Lead:",-1),Me={class:"p-6"},De=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Add Activity",-1),je=["onSubmit"],qe=["value"],Be={class:"border-t pt-4"},Le={class:"flex items-center space-x-2"},Te={key:0,class:"mt-3"},Ie={class:"flex justify-end space-x-3"},Ee={class:"p-6"},Oe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Update Status",-1),Re=["onSubmit"],We=["value"],ze={class:"flex justify-end space-x-3"},Ge={class:"p-6"},Qe=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule Follow-up",-1),Ye=["onSubmit"],Ze={class:"flex justify-end space-x-3"},as={__name:"Show",props:{prospect:Object},setup(e){const w=e,h=U(!1),y=U(!1),v=U(!1),u=k({activity_type:"note_added",title:"",description:"",activity_date:new Date().toISOString().slice(0,10),schedule_followup:!1,followup_date:""}),_=k({status:w.prospect.status,notes:""}),b=k({next_follow_up_at:"",notes:""});k({});const q=()=>{u.post(route("prospects.addActivity",w.prospect.id),{onSuccess:()=>{h.value=!1,u.reset(),u.schedule_followup=!1,u.followup_date=""}})},B=()=>{_.patch(route("prospects.updateStatus",w.prospect.id),{onSuccess:()=>{y.value=!1,_.reset()}})},L=()=>{b.post(route("prospects.scheduleFollowUp",w.prospect.id),{onSuccess:()=>{v.value=!1,b.reset()}})},T=r=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800",I=r=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800",P=r=>{if(!r)return"-";const o=new Date(r),s=String(o.getDate()).padStart(2,"0"),z=String(o.getMonth()+1).padStart(2,"0"),G=o.getFullYear();return`${s}-${z}-${G}`},E=r=>r?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r):"-",O=r=>r.charAt(0).toUpperCase()+r.slice(1).replace("_"," "),R=[{value:"email_sent",label:"Email Sent"},{value:"email_received",label:"Email Received"},{value:"call_made",label:"Call Made"},{value:"call_received",label:"Call Received"},{value:"meeting_scheduled",label:"Meeting Scheduled"},{value:"meeting_completed",label:"Meeting Completed"},{value:"note_added",label:"Note Added"},{value:"linkedin_message",label:"LinkedIn Message"},{value:"proposal_sent",label:"Proposal Sent"},{value:"follow_up_scheduled",label:"Follow-up Scheduled"},{value:"document_shared",label:"Document Shared"},{value:"other",label:"Other"}],W=["new","contacted","qualified","unqualified","converted","lost"];return(r,o)=>(i(),d(S,null,[a(n(Q),{title:"Prospects"}),a(Z,null,{default:m(()=>[t("div",H,[t("div",J,[t("div",null,[t("h1",K,"Prospect: "+l(e.prospect.first_name)+" "+l(e.prospect.last_name),1),X]),t("div",tt,[a(j,{href:r.route("prospects.index")},{svg:m(()=>[et]),_:1},8,["href"])])]),t("div",st,[t("div",ot,[t("div",lt,[at,t("div",it,[t("div",null,[dt,t("p",nt,l(e.prospect.first_name)+" "+l(e.prospect.last_name),1)]),t("div",null,[ct,t("p",rt,l(e.prospect.email||"N/A"),1)]),e.prospect.phone?(i(),d("div",ut,[mt,t("p",pt,l(e.prospect.phone),1)])):c("",!0),e.prospect.company?(i(),d("div",ft,[_t,t("p",bt,l(e.prospect.company),1)])):c("",!0),e.prospect.position?(i(),d("div",xt,[gt,t("p",ht,l(e.prospect.position??"N/A"),1)])):c("",!0),e.prospect.country||e.prospect.city?(i(),d("div",yt,[vt,t("p",wt,l(e.prospect.city)+l(e.prospect.city&&e.prospect.country?", ":"")+l(e.prospect.country),1)])):c("",!0)])]),t("div",kt,[St,e.prospect.activities&&e.prospect.activities.length>0?(i(),d("div",Ct,[(i(!0),d(S,null,A(e.prospect.activities,s=>(i(),d("div",{key:s.id,class:"flex items-start space-x-3 p-4 bg-gray-50 rounded-lg"},[t("div",Ut,[t("div",At,[t("span",Vt,l(s.activity_type.charAt(0).toUpperCase()),1)])]),t("div",$t,[t("div",Ft,[t("p",Nt,l(s.title),1)]),s.description?(i(),d("p",Pt,l(s.description),1)):c("",!0),t("div",Mt,[s.user?(i(),d("p",Dt,"by "+l(s.user.first_name),1)):c("",!0),t("p",jt,l(P(s.activity_date)),1)])])]))),128))])):(i(),d("div",qt," No activities recorded yet. "))])]),t("div",Bt,[t("div",Lt,[Tt,t("div",It,[a(C,{onClick:o[0]||(o[0]=s=>h.value=!0)},{default:m(()=>[p(" Add Activity ")]),_:1}),a(g,{class:"w-full",onClick:o[1]||(o[1]=s=>y.value=!0)},{default:m(()=>[p(" Update Status ")]),_:1}),a(g,{class:"w-full",onClick:o[2]||(o[2]=s=>v.value=!0)},{default:m(()=>[p(" Schedule Follow-up ")]),_:1}),a(g,{class:"w-full",onClick:o[3]||(o[3]=s=>r.$inertia.visit(r.route("prospects.edit",e.prospect.id)))},{default:m(()=>[p(" Edit Prospect ")]),_:1})])]),e.prospect.initial_conversation||e.prospect.notes||e.prospect.requirements?(i(),d("div",Et,[Ot,t("div",Rt,[e.prospect.initial_conversation?(i(),d("div",Wt,[zt,t("p",Gt,l(e.prospect.initial_conversation),1)])):c("",!0),e.prospect.requirements?(i(),d("div",Qt,[Yt,t("p",Zt,l(e.prospect.requirements),1)])):c("",!0),e.prospect.notes?(i(),d("div",Ht,[Jt,t("p",Kt,l(e.prospect.notes),1)])):c("",!0)])])):c("",!0),e.prospect.linkedin_url||e.prospect.website_url||e.prospect.company_website?(i(),d("div",Xt,[te,t("div",ee,[e.prospect.linkedin_url?(i(),d("a",{key:0,href:e.prospect.linkedin_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," LinkedIn Profile ",8,se)):c("",!0),e.prospect.website_url?(i(),d("a",{key:1,href:e.prospect.website_url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Personal Website ",8,oe)):c("",!0),e.prospect.company_website?(i(),d("a",{key:2,href:e.prospect.company_website,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800"}," Company Website ",8,le)):c("",!0)])])):c("",!0),t("div",ae,[ie,t("div",de,[t("div",ne,[ce,t("span",{class:M([T(e.prospect.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.status.charAt(0).toUpperCase()+e.prospect.status.slice(1)),3)]),t("div",re,[ue,t("span",{class:M([I(e.prospect.priority),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.priority.charAt(0).toUpperCase()+e.prospect.priority.slice(1)),3)]),t("div",null,[me,t("span",pe,l(e.prospect.score)+"/100",1)]),t("div",null,[fe,t("span",_e,l(O(e.prospect.lead_source)),1)]),e.prospect.assigned_user?(i(),d("div",be,[xe,t("span",ge,l(e.prospect.assigned_user.name),1)])):c("",!0)])]),t("div",he,[ye,t("div",ve,[e.prospect.project_type?(i(),d("div",we,[ke,t("p",Se,l(e.prospect.project_type),1)])):c("",!0),e.prospect.estimated_budget?(i(),d("div",Ce,[Ue,t("p",Ae,l(E(e.prospect.estimated_budget)),1)])):c("",!0),e.prospect.next_follow_up_at?(i(),d("div",Ve,[$e,t("p",Fe,l(P(e.prospect.next_follow_up_at)),1)])):c("",!0),e.prospect.converted_lead?(i(),d("div",Ne,[Pe,a(j,{href:r.route("leads.show",e.prospect.converted_lead.id),class:"ml-2 text-sm text-indigo-600"},{default:m(()=>[p(" View Lead #"+l(e.prospect.converted_lead.id),1)]),_:1},8,["href"])])):c("",!0)])])])])]),a(F,{show:h.value,onClose:o[10]||(o[10]=s=>h.value=!1)},{default:m(()=>[t("div",Me,[De,t("form",{onSubmit:V(q,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"activity_type",value:"Activity Type"}),x(t("select",{id:"activity_type","onUpdate:modelValue":o[4]||(o[4]=s=>n(u).activity_type=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(R,s=>t("option",{key:s.value,value:s.value},l(s.label),9,qe)),64))],512),[[D,n(u).activity_type]])]),t("div",null,[a(f,{for:"activity_title",value:"Title"}),a(N,{id:"activity_title",modelValue:n(u).title,"onUpdate:modelValue":o[5]||(o[5]=s=>n(u).title=s),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[a(f,{for:"activity_description",value:"Description"}),x(t("textarea",{id:"activity_description","onUpdate:modelValue":o[6]||(o[6]=s=>n(u).description=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[$,n(u).description]])]),t("div",Be,[t("div",Le,[x(t("input",{id:"schedule_followup","onUpdate:modelValue":o[7]||(o[7]=s=>n(u).schedule_followup=s),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[Y,n(u).schedule_followup]]),a(f,{for:"schedule_followup",value:"Schedule Follow-up",class:"text-sm font-medium text-gray-700"})]),n(u).schedule_followup?(i(),d("div",Te,[a(f,{for:"followup_date",value:"Follow-up Date"}),a(N,{id:"followup_date",modelValue:n(u).followup_date,"onUpdate:modelValue":o[8]||(o[8]=s=>n(u).followup_date=s),type:"date",class:"mt-1 block w-full",min:new Date().toISOString().slice(0,10)},null,8,["modelValue","min"])])):c("",!0)]),t("div",Ie,[a(g,{onClick:o[9]||(o[9]=s=>h.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(u).processing},{default:m(()=>[p("Add Activity")]),_:1},8,["disabled"])])],40,je)])]),_:1},8,["show"]),a(F,{show:y.value,onClose:o[14]||(o[14]=s=>y.value=!1)},{default:m(()=>[t("div",Ee,[Oe,t("form",{onSubmit:V(B,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"status",value:"Status"}),x(t("select",{id:"status","onUpdate:modelValue":o[11]||(o[11]=s=>n(_).status=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(i(),d(S,null,A(W,s=>t("option",{key:s,value:s},l(s.charAt(0).toUpperCase()+s.slice(1)),9,We)),64))],512),[[D,n(_).status]])]),t("div",null,[a(f,{for:"status_notes",value:"Notes"}),x(t("textarea",{id:"status_notes","onUpdate:modelValue":o[12]||(o[12]=s=>n(_).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Optional notes about the status change"},null,512),[[$,n(_).notes]])]),t("div",ze,[a(g,{onClick:o[13]||(o[13]=s=>y.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(_).processing},{default:m(()=>[p("Update Status")]),_:1},8,["disabled"])])],40,Re)])]),_:1},8,["show"]),a(F,{show:v.value,onClose:o[18]||(o[18]=s=>v.value=!1)},{default:m(()=>[t("div",Ge,[Qe,t("form",{onSubmit:V(L,["prevent"]),class:"space-y-4"},[t("div",null,[a(f,{for:"follow_up_date",value:"Follow-up Date"}),a(N,{id:"follow_up_date",modelValue:n(b).next_follow_up_at,"onUpdate:modelValue":o[15]||(o[15]=s=>n(b).next_follow_up_at=s),type:"date",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[a(f,{for:"follow_up_notes",value:"Notes"}),x(t("textarea",{id:"follow_up_notes","onUpdate:modelValue":o[16]||(o[16]=s=>n(b).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Notes about the follow-up"},null,512),[[$,n(b).notes]])]),t("div",Ze,[a(g,{onClick:o[17]||(o[17]=s=>v.value=!1)},{default:m(()=>[p("Cancel")]),_:1}),a(C,{disabled:n(b).processing},{default:m(()=>[p("Schedule Follow-up")]),_:1},8,["disabled"])])],40,Ye)])]),_:1},8,["show"])]),_:1})],64))}};export{as as default};
