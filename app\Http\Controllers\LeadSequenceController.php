<?php

namespace App\Http\Controllers;

use App\Models\Leads;
use App\Models\LeadSequence;
use App\Models\Sequences;
use App\Models\MailConfig;
use App\Models\EmailTag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Helpers\MailHelper;
use Illuminate\Support\Facades\Mail;
use App\Mail\BulkEmail;
use App\Jobs\SendSequenceEmailJob;
use Inertia\Inertia;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LeadSequenceController extends Controller
{
    public function index(Request $request)
    {
        $search        = $request->input('search');
        $leadId        = $request->input('lead_id');
        $sequenceId    = $request->input('sequence_id');
        $statusId      = $request->input('status');
        $scheduledDate = $request->input('scheduled_date');

        $query = LeadSequence::with('lead', 'sequence');

        // Exclude failed emails from the main list - they will be shown in the failed emails list
        $query->where('status', '!=', 'failed');

        if($search){
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                ->orWhere('content', 'like', "%{$search}%")
                ->orWhereHas('lead', function ($q2) use ($search) {
                    $q2->where('email', 'like', "%{$search}%");
                });
            });
        }

        if ($scheduledDate === 'today') {
            $query->whereDate('next_scheduled_at', Carbon::today());
        }

        if ($leadId) {
            $query->where('lead_id', $leadId);
        }

        if ($sequenceId) {
            $query->where('sequence_id', $sequenceId);
        }

        if ($statusId) {
            $query->where('status', $statusId);
        }

        $sequences = Sequences::whereNotNull('name')
            ->orderBy('name', 'asc')
            ->get(['id', 'name']);

        $status      = config('constants.leadSequenceStatus');
        $statusOrder = ['pending', 'completed'];

        $query->orderByRaw(
            "FIELD(status, '" . implode("','", $statusOrder) . "') ASC"
        );

        $data = $query->orderBy('id', 'desc')->paginate(20);

        return Inertia::render('LeadSequence/List', [
            'data'      => $data,
            'sequences' => $sequences,
            'status'    => $status,
            'filters'   => $request->only([
                'search',
                'lead_id',
                'sequence_id',
                'status',
                'scheduled_date'
            ]),
        ]);
    }

    public function assignLeadToSequence(Request $request)
    {
        $ids = $request->input('lead_ids');
        if (! is_array($ids)) {
            $ids = json_decode($ids, true);
        }

        if (empty($ids)) {
            return Redirect::to('/leads')
                ->with('error', 'No leads selected');
        }

        $leads = Leads::whereIn('id', $ids)->get();
        if ($leads->isEmpty()) {
            return Redirect::to('/leads')
                ->with('error', 'No valid leads found');
        }

        $sequence = Sequences::find($request->input('sequence_id'));
        if (! $sequence) {
            return Redirect::to('/leads')
                ->with('error', 'Sequence not found');
        }

        $mailConfig = $sequence->mailConfig;

        $leadIds       = $leads->pluck('id')->toArray();
        $existingLeads = LeadSequence::whereIn('lead_id', $leadIds)
            ->where('sequence_id', $sequence->id)
            ->pluck('lead_id')
            ->toArray();

        if ($existingLeads) {
            return Redirect::to('/leads')
                ->with('error', 'Leads ' . implode(', ', $existingLeads) . ' are already in this sequence');
        }

        $steps = $sequence->steps()
            ->orderBy('step_number')
            ->get();

        if ($steps->isEmpty()) {
            return Redirect::to('/leads')
                ->with('error', 'No steps found in sequence');
        }

        foreach ($leads as $lead) {
            $scheduledAt = Carbon::now();

            foreach ($steps as $step) {
                if ($step->step_number > 1) {
                    $scheduledAt = $scheduledAt->copy()
                        ->addDays($step->days_after_previous_step);
                }

                $replacements = [
                    '{email}'                      => $lead->email ?? '',
                    '{first_name}'                 => $lead->first_name ?? '',
                    '{last_name}'                  => $lead->last_name ?? '',
                    '{designation}'                => $lead->designation ?? '',
                    '{city}'                       => $lead->city ?? '',
                    '{country}'                    => $lead->country ?? '',
                    '{organization_name}'          => $lead->organization_name ?? '',
                    '{organization_website_url}'   => $lead->organization_website_url ?? '',
                    '{linkedin_url}'               => $lead->linkedin_url ?? '',
                    '{organization_linkedin_url}'  => $lead->organization_linkedin_url ?? '',
                ];

                $processedSubject = str_ireplace(
                    array_keys($replacements),
                    array_values($replacements),
                    $step->subject
                );

                $contentReplacements = $replacements + [
                    '{subject}' => $processedSubject,
                ];

                $processedContent = str_ireplace(
                    array_keys($contentReplacements),
                    array_values($contentReplacements),
                    $step->content
                );

                LeadSequence::create([
                    'lead_id'           => $lead->id,
                    'sequence_id'       => $sequence->id,
                    'current_step'      => $step->step_number,
                    'next_scheduled_at' => $scheduledAt,
                    'subject'           => $processedSubject,
                    'content'           => $processedContent,
                    'status'            => 'pending',
                ]);

                // prepare for next step
                $scheduledAt = $scheduledAt->copy();
            }
        }

        return Redirect::to('/leads')
            ->with('success', 'Leads assigned to sequence successfully');
    }

    public function edit($id)
    {
        $data = LeadSequence::where('id', $id)
            ->with('lead', 'sequence')
            ->get()
            ->toArray();

        $tags = EmailTag::select('id', 'name', 'description')->get();

        return Inertia::render('LeadSequence/Edit', compact('data', 'tags'));
    }

    public function update(Request $request)
    {
        $leadSequence = LeadSequence::findOrFail($request->id);
        $leadSequence->load(['lead', 'sequence.mailConfig']);

        $emailTags = EmailTag::pluck('name')
            ->map(fn($tag) => trim($tag, '{}'));

        $replacements = [];
        foreach ($emailTags as $field) {
            if ($field === 'signature') {
                continue; // skip signature entirely
            }

            if (isset($leadSequence->lead->$field)) {
                $replacements["{{$field}}"] = $leadSequence->lead->$field;
            } elseif (isset($leadSequence->sequence->mailConfig->$field)) {
                $replacements["{{$field}}"] = $leadSequence->sequence->mailConfig->$field;
            }
        }

        $processedContent = str_ireplace(
            array_keys($replacements),
            array_values($replacements),
            $request->content
        );

        $leadSequence->update([
            'content' => $processedContent,
        ]);

        return Redirect::to('sent-email')
            ->with('success', 'Lead sequence updated successfully');
    }

    public function updateFailed(Request $request)
    {
        // dd($request->all());

        DB::beginTransaction();
        try {
            $leadSequence = LeadSequence::findOrFail($request->id);
            $lead = Leads::findOrFail($leadSequence->lead_id);
            $lead->update(['email' => $request->email]);
            if ($request->reschedule) {
                // Get all lead_sequences for this lead and sequence
                $sequences = LeadSequence::where('lead_id', $lead->id)
                    ->where('sequence_id', $leadSequence->sequence_id)
                    ->orderBy('current_step')
                    ->get();

                $scheduledAt = Carbon::now();

                foreach ($sequences as $seq) {
                    if ($seq->current_step > 1) {
                        // Get days_after_previous_step for this step
                        $step = $seq->sequence->steps()->where('step_number', $seq->current_step)->first();
                        $scheduledAt = $scheduledAt->copy()->addDays($step->days_after_previous_step ?? 0);
                    }

                    $seq->update([
                        'status' => 'pending',
                        'next_scheduled_at' => $scheduledAt,
                        'sent_time' => null,
                        'opened_at' => null,
                        'clicked_at' => null,
                    ]);

                    $scheduledAt = $scheduledAt->copy(); // prepare for next step
                }
            }
            DB::commit();

            Redirect::to('/failed-emails')->with('success', 'Lead sequence updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::to('/leads')->with('error', $e->getMessage());
        }
    }

    public function sendScheduledEmails()
    {
        $today = Carbon::today()->toDateString();

        $scheduledEmails = LeadSequence::whereDate('next_scheduled_at', $today)
            ->where('status', 'pending')
            ->get(['id', 'next_scheduled_at']);

        foreach ($scheduledEmails as $email) {
            $scheduledTime = Carbon::parse($email->next_scheduled_at);
            SendSequenceEmailJob::dispatch($email->id)
                ->delay($scheduledTime);
        }

        return Redirect::to('/sent-email')
            ->with('success', 'Emails are sending.');
    }

    public function destroy($id)
    {
        try {
            LeadSequence::findOrFail($id)->delete();

            return Redirect::to('/sent-email')
                ->with('success', 'Lead Sequence Deleted Successfully');
        } catch (\Exception $e) {
            return Redirect::to('/sent-email')
                ->with('error', 'An error occurred while deleting the Lead Sequence.');
        }
    }

    public function preview($id)
    {
        $leadSequence = LeadSequence::with(['sequence.mailConfig', 'lead'])
            ->find($id);

        if (!$leadSequence) {
            return response()->json([
                'success' => false,
                'message' => 'Lead sequence not found'
            ], 404);
        }

        $mailConfig = $leadSequence->sequence->mailConfig;
        $lead = $leadSequence->lead;

        if (!$mailConfig) {
            return response()->json([
                'success' => false,
                'message' => 'Mail config not found for sequence'
            ], 400);
        }

        try {
            // Get the original content
            $originalContent = $leadSequence->content;

            // Handle differently based on email status
            $isSent = $leadSequence->status === 'completed';

            // For sent emails, we use the stored full content that includes the company info at the time of sending
            // For pending emails, we add the current company info
            if ($isSent) {
                // Use the stored full content or fall back to original content
                $finalContent = $leadSequence->full_content ?: $originalContent;
                $templateContent = $finalContent;
            } else {
                // For pending emails, add the current company info
                $companyInfoHtml = $mailConfig->companyInfo ?? '';
                $finalContent = "{$originalContent}{$companyInfoHtml}";
                $templateContent = "{$originalContent}{$companyInfoHtml}";
            }

            // Create the preview response
            $preview = [
                'success' => true,
                'subject' => $leadSequence->subject,
                'content' => $finalContent,
                'originalContent' => $templateContent,
                'recipient' => $lead->email,
                'isSent' => $isSent,
                'sentTime' => $leadSequence->sent_time,
                'from' => [
                    'email' => $mailConfig->email,
                    'name' => $mailConfig->name
                ],
                'lead' => [
                    'first_name' => $lead->first_name,
                    'last_name' => $lead->last_name,
                    'email' => $lead->email,
                    'organization' => $lead->organization_name
                ]
            ];

            return response()->json($preview);

        } catch (\Throwable $e) {
            Log::error("Email preview generation failed: " . $e->getMessage(), [
                'lead_sequence_id' => $id,
                'exception' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => "Failed to generate preview: " . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display a listing of failed emails
     */
    public function failedEmails(Request $request)
    {
        $search     = $request->input('search');
        $sequenceId = $request->input('sequence_id');

        $query = LeadSequence::with('lead', 'sequence')
            ->where('status', 'failed');

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('lead', function ($q) use ($search) {
                      $q->where('email', 'like', "%{$search}%");
                  });
            });
        }

        if ($sequenceId) {
            $query->where('sequence_id', $sequenceId);
        }

        $sequences = Sequences::whereNotNull('name')
            ->orderBy('name', 'asc')
            ->get(['id', 'name']);

        $data = $query->orderBy('id', 'desc')->paginate(20);

        return Inertia::render('LeadSequence/FailedList', [
            'data'      => $data,
            'sequences' => $sequences,
            'filters'   => $request->only([
                'search',
                'sequence_id',
            ]),
        ]);
    }

}
