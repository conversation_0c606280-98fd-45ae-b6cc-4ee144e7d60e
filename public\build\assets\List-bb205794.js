import{_ as w,a as v,b}from"./AdminLayout-13d09996.js";import{_ as u}from"./CreateButton-9e45a5d4.js";import{_ as k}from"./SecondaryButton-5ec31741.js";import{D as C}from"./DangerButton-304eb6b1.js";import{M as R}from"./Modal-9f7e750d.js";import{T as B,r as f,o as s,c as l,a as o,u as L,w as t,F as p,Z as M,b as e,g as r,f as _,d as j,t as N,e as $}from"./app-b1cf8b3a.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const E={class:"animate-top"},V={class:"flex justify-between items-center"},z=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Roles & Permissions")],-1),A={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},O={key:0,class:"flex justify-end"},P={class:"mt-8 flow-root"},T={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},F={class:"overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px"}},I={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg mb-20"},S={class:"min-w-full divide-y divide-gray-300"},H=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"ROLE NAME"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),Z={key:0,class:"divide-y divide-gray-300 bg-white"},q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},G={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},J={class:"flex justify-start gap-4"},K=e("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Q=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),U=e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),W=["onClick"],X=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Y=e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),ee=[X,Y],te={key:1},se=e("tr",{class:"bg-white"},[e("td",{colspan:"3",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),oe=[se],le={class:"p-6"},ae=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Role & Permissions? ",-1),ie={class:"mt-6 flex justify-end"},xe={__name:"List",props:["roles","permissions"],setup(i){const c=i,g=B({}),d=f(!1),h=f(null),x=a=>{h.value=a,d.value=!0},m=()=>{d.value=!1},y=()=>{g.delete(route("roles.destroy",{id:h.value}),{onSuccess:()=>m()})};return(a,ne)=>(s(),l(p,null,[o(L(M),{title:"Role-Permission"}),o(w,null,{default:t(()=>[e("div",E,[e("div",V,[z,e("div",A,[e("div",D,[o(u,{href:a.route("setting")},{default:t(()=>[r(" Back ")]),_:1},8,["href"])]),i.permissions.canCreateRoles?(s(),l("div",O,[o(u,{href:a.route("roles.create")},{default:t(()=>[r(" Add Role ")]),_:1},8,["href"])])):_("",!0)])]),e("div",P,[e("div",T,[e("div",F,[e("div",I,[e("table",S,[H,c.roles&&c.roles.length>0?(s(),l("tbody",Z,[(s(!0),l(p,null,j(c.roles,(n,re)=>(s(),l("tr",{key:n.id,class:""},[e("td",q,N(n.name),1),e("td",G,[e("div",J,[o(v,{align:"right",width:"48"},{trigger:t(()=>[K]),content:t(()=>[i.permissions.canEditRoles?(s(),$(b,{key:0,href:a.route("roles.edit",{id:n.id})},{svg:t(()=>[Q]),text:t(()=>[U]),_:2},1032,["href"])):_("",!0),i.permissions.canDeleteRoles?(s(),l("button",{key:1,type:"button",onClick:ce=>x(n.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ee,8,W)):_("",!0)]),_:2},1024)])])]))),128))])):(s(),l("tbody",te,oe))])])])])])]),o(R,{show:d.value,onClose:m},{default:t(()=>[e("div",le,[ae,e("div",ie,[o(k,{onClick:m},{default:t(()=>[r(" Cancel")]),_:1}),o(C,{class:"ml-3",onClick:y},{default:t(()=>[r(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{xe as default};
