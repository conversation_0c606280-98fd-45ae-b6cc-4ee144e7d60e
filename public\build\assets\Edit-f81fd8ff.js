import{K as v,o as l,c as m,a,u as t,w as c,F as _,Z as x,b as r,h,t as d,f as i,g as b,A as g}from"./app-b1cf8b3a.js";import{_ as V,b as w}from"./AdminLayout-13d09996.js";/* empty css                                                              */import{_ as p}from"./InputLabel-352dc786.js";import{_ as u}from"./TextInput-796570c1.js";import{P as k}from"./PrimaryButton-05be7944.js";import{Q as C}from"./vue-quill.snow-43335c5e.js";import{u as $}from"./index-2c2326ed.js";import"./_plugin-vue_export-helper-c27b6911.js";const S={class:"animate-top"},U={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},E=r("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit SMTP",-1),I=["onSubmit"],P={class:"border-b border-gray-900/10 pb-12"},N={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},T={class:"sm:col-span-3"},j={key:0,class:"mt-1 text-sm text-red-500"},B={class:"sm:col-span-3"},F={key:0,class:"mt-1 text-sm text-red-500"},M={class:"sm:col-span-3"},O={key:0,class:"mt-1 text-sm text-red-500"},Q={class:"sm:col-span-3"},A={key:0,class:"mt-1 text-sm text-red-500"},H={class:"sm:col-span-3"},K={key:0,class:"mt-1 text-sm text-red-500"},Z={class:"sm:col-span-3"},q={key:0,class:"mt-1 text-sm text-red-500"},z={class:"sm:col-span-3"},D={key:0,class:"mt-1 text-sm text-red-500"},G={class:"sm:col-span-6"},J={key:0,class:"mt-1 text-sm text-red-500"},L={class:"flex mt-12 items-center justify-between"},R={class:"ml-auto flex items-center justify-end gap-x-6"},W=r("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),X={key:0,class:"text-sm text-gray-600"},ie={__name:"Edit",props:{data:{type:Object},filepath:{type:Object}},setup(Y){const n=v().props.data,e=$("post","/smtp",{id:n.id,host:n?n.host:"",port:n?n.port:"",username:n?n.username:"",password:n?n.password:"",email:n?n.email:"",encryption:n?n.encryption:"",name:n?n.name:"",companyInfo:n==null?void 0:n.companyInfo}),y=()=>{e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})};return(f,o)=>(l(),m(_,null,[a(t(x),{title:"SMTP"}),a(V,null,{default:c(()=>[r("div",S,[r("div",U,[E,r("form",{onSubmit:h(y,["prevent"]),class:""},[r("div",P,[r("div",N,[r("div",T,[a(p,{for:"host",value:"Host"}),a(u,{id:"host",type:"text",modelValue:t(e).host,"onUpdate:modelValue":o[0]||(o[0]=s=>t(e).host=s),autocomplete:"host",onChange:o[1]||(o[1]=s=>t(e).validate("host"))},null,8,["modelValue"]),t(e).errors.host?(l(),m("p",j,d(t(e).errors.host),1)):i("",!0)]),r("div",B,[a(p,{for:"port",value:"Port"}),a(u,{id:"port",type:"text",modelValue:t(e).port,"onUpdate:modelValue":o[2]||(o[2]=s=>t(e).port=s),autocomplete:"port",onChange:o[3]||(o[3]=s=>t(e).validate("port"))},null,8,["modelValue"]),t(e).errors.port?(l(),m("p",F,d(t(e).errors.port),1)):i("",!0)]),r("div",M,[a(p,{for:"username",value:"Username"}),a(u,{id:"username",type:"text",modelValue:t(e).username,"onUpdate:modelValue":o[4]||(o[4]=s=>t(e).username=s),autocomplete:"username",onChange:o[5]||(o[5]=s=>t(e).validate("username"))},null,8,["modelValue"]),t(e).errors.username?(l(),m("p",O,d(t(e).errors.username),1)):i("",!0)]),r("div",Q,[a(p,{for:"password",value:"Password"}),a(u,{id:"password",type:"text",modelValue:t(e).password,"onUpdate:modelValue":o[6]||(o[6]=s=>t(e).password=s),autocomplete:"password",onChange:o[7]||(o[7]=s=>t(e).validate("password"))},null,8,["modelValue"]),t(e).errors.password?(l(),m("p",A,d(t(e).errors.password),1)):i("",!0)]),r("div",H,[a(p,{for:"email",value:"Email"}),a(u,{id:"email",type:"text",modelValue:t(e).email,"onUpdate:modelValue":o[8]||(o[8]=s=>t(e).email=s),autocomplete:"email",onChange:o[9]||(o[9]=s=>t(e).validate("email"))},null,8,["modelValue"]),t(e).errors.email?(l(),m("p",K,d(t(e).errors.email),1)):i("",!0)]),r("div",Z,[a(p,{for:"encryption",value:"Encryption"}),a(u,{id:"encryption",type:"text",modelValue:t(e).encryption,"onUpdate:modelValue":o[10]||(o[10]=s=>t(e).encryption=s),autocomplete:"encryption",onChange:o[11]||(o[11]=s=>t(e).validate("encryption"))},null,8,["modelValue"]),t(e).errors.encryption?(l(),m("p",q,d(t(e).errors.encryption),1)):i("",!0)]),r("div",z,[a(p,{for:"name",value:"Name"}),a(u,{id:"name",type:"text",modelValue:t(e).name,"onUpdate:modelValue":o[12]||(o[12]=s=>t(e).name=s),autocomplete:"name",onChange:o[13]||(o[13]=s=>t(e).validate("name"))},null,8,["modelValue"]),t(e).errors.name?(l(),m("p",D,d(t(e).errors.name),1)):i("",!0)]),r("div",G,[a(p,{value:"Company Info"}),a(t(C),{content:t(e).companyInfo,"onUpdate:content":o[14]||(o[14]=s=>t(e).companyInfo=s),contentType:"html",theme:"snow",toolbar:"full",options:{modules:{toolbar:[["bold","italic","underline"],["link"],[{list:"ordered"},{list:"bullet"}],["clean"]]},placeholder:"Enter company info"},class:"h-64 mb-8"},null,8,["content"]),t(e).errors.companyInfo?(l(),m("p",J,d(t(e).errors.companyInfo),1)):i("",!0)])])]),r("div",L,[r("div",R,[a(w,{href:f.route("smtp.index")},{svg:c(()=>[W]),_:1},8,["href"]),a(k,{disabled:t(e).processing},{default:c(()=>[b("Save")]),_:1},8,["disabled"]),a(g,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[t(e).recentlySuccessful?(l(),m("p",X,"Saved.")):i("",!0)]),_:1})])])],40,I)])])]),_:1})],64))}};export{ie as default};
