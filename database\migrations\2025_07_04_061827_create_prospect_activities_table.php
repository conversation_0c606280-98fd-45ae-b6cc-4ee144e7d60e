<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prospect_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prospect_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who performed the activity

            $table->enum('activity_type', [
                'email_sent', 'email_received', 'call_made', 'call_received',
                'meeting_scheduled', 'meeting_completed', 'note_added',
                'status_changed', 'linkedin_message', 'proposal_sent',
                'follow_up_scheduled', 'document_shared', 'other'
            ]);

            $table->string('title'); // Brief title of the activity
            $table->text('description')->nullable(); // Detailed description
            $table->json('metadata')->nullable(); // Additional data (email subject, call duration, etc.)

            $table->timestamp('activity_date'); // When the activity occurred
            $table->timestamps();

            // Indexes
            $table->index(['prospect_id', 'activity_date']);
            $table->index(['activity_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prospect_activities');
    }
};
