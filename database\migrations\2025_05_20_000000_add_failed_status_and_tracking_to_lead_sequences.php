<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, modify the enum to include 'failed' status if it doesn't already exist
        DB::statement("ALTER TABLE lead_sequences MODIFY COLUMN status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending'");

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to remove these columns in the down migration
        // as they might contain important data
    }
};
