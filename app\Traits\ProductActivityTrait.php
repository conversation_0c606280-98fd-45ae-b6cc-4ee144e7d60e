<?php

namespace App\Traits;

use App\Models\ProductLog;
use Illuminate\Database\Eloquent\Model;

trait ProductActivityTrait {

    public static function addProductLog(Model $model, $data, $productId, $organizationId, string $type = 'updated')
    {
        $logData = [];
        if($type == 'updated' || $type == 'edit-stock'){
            $model->fill($data);
            $dirtyAttributes = $model->getDirty();

            $originalAttributes = $model->getOriginal();

            foreach ($dirtyAttributes as $key => $value) {
                $oldValue = $originalAttributes[$key] ?? null;
                if ((string)$oldValue !== (string)$value) {
                    $logData[$key] = [
                        'old' => $oldValue,
                        'new' => $value
                    ];
                }
            }
        }else if ($type == 'add-stock' || $type == 'received' || $type == 'payment'){
            $logData = $data;
        }else{
            $logData = [$type];
        }

        if (!empty($logData)) {
            //$modelClass = get_class($model);
            ProductLog::create([
                'product_id'    => $productId,
                'organization_id'=> $organizationId,
                'type'          => $type,
                'log_data'      => json_encode($logData),
                'created_by'    => auth()->id()
            ]);
        }
    }

}
