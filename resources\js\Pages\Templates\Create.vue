<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';

const form = useForm({
    subject: '',
    content: '',
});

const submit = () => {
    form.post(route('templates.store'));
};
</script>

<template>
    <Head title="Template" />

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Template</h1>
                </div>
                <div>
                    <Link :href="route('templates.index')" class="text-sm text-gray-600 hover:text-gray-900">
                        ← Back to Templates
                    </Link>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg">
                <form @submit.prevent="submit" class="p-6 space-y-6">
                    <div>
                        <InputLabel for="subject" value="Subject" />
                        <TextInput
                            id="subject"
                            type="text"
                            class="mt-1 block w-full"
                            v-model="form.subject"
                            required
                            autofocus
                            placeholder="Enter template subject"
                        />
                        <InputError class="mt-2" :message="form.errors.subject" />
                    </div>

                    <div>
                        <InputLabel for="content" value="Content" />
                        <TextArea
                            id="content"
                            class="mt-1 block w-full"
                            v-model="form.content"
                            rows="10"
                            required
                            placeholder="Enter template content..."
                        />
                        <InputError class="mt-2" :message="form.errors.content" />
                    </div>
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                            <SvgLink :href="route('templates.index')">
                                    <template #svg>
                                        <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                                    </template>
                            </SvgLink>
                            <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
