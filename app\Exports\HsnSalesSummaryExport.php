<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class HsnSalesSummaryExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;

    public function __construct(array $allData, $organizationName = null, $fromDate = null, $toDate = null)
    {
        $this->allData = $allData;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
    }

    public function title(): string
    {
        return 'HSN Sales Summary Report as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        $headings = [];

        if ($this->organizationName) {
            $headings[] = ["Organization: " . $this->organizationName];
        }

        if ($this->fromDate && $this->toDate) {
            $headings[] = ["From Date: " . $this->fromDate . " To Date: " . $this->toDate];
        }

        $headings[] = [
            "HSN Code",
            "Date",
            "Bill No",
            "Quantity",
            "Taxable",
            "SGST",
            "CGST",
            "IGST",
        ];

        return $headings;
    }

    public function collection()
    {
        $data = $this->allData;
        $columns = [];

        $hsnGroups = [];
        foreach ($data as $d) {
            foreach ($d['invoice_detail'] as $detail) {
                $hsnCode = $detail['product']['hsn_code'] ?? 'N/A';
                $hsnGroups[$hsnCode]['details'][] = [
                    'date' => $d['date'],
                    'invoice_no' => $d['invoice_no'],
                    'qty' => $detail['qty'],
                    'sub_total' => $detail['total_price'],
                    'sgst' => ($d['sgst'] > 0 ) ? $detail['total_gst_amount']/2 : '0.00',
                    'cgst' => ($d['cgst'] > 0 ) ? $detail['total_gst_amount']/2 : '0.00',
                    'igst' => ($d['igst'] > 0 ) ? $detail['total_gst_amount'] : '0.00',
                ];

                $hsnGroups[$hsnCode]['totals']['qty'] = ($hsnGroups[$hsnCode]['totals']['qty'] ?? 0) + $detail['qty'];
                $hsnGroups[$hsnCode]['totals']['sub_total'] = ($hsnGroups[$hsnCode]['totals']['sub_total'] ?? 0) + $detail['total_price'];
                $hsnGroups[$hsnCode]['totals']['sgst'] = ($hsnGroups[$hsnCode]['totals']['sgst'] ?? 0) + (($d['sgst'] > 0 ) ? $detail['total_gst_amount']/2 : '0.00');
                $hsnGroups[$hsnCode]['totals']['cgst'] = ($hsnGroups[$hsnCode]['totals']['cgst'] ?? 0) + (($d['cgst'] > 0 ) ? $detail['total_gst_amount']/2 : '0.00');
                $hsnGroups[$hsnCode]['totals']['igst'] = ($hsnGroups[$hsnCode]['totals']['igst'] ?? 0) + (($d['igst'] > 0 ) ? $detail['total_gst_amount'] : 0.00);
            }
        }

        foreach ($hsnGroups as $hsnCode => $group) {
            $columns[] = [
                $hsnCode,
                '', '',
                $group['totals']['sub_total'],
                $group['totals']['sgst'],
                $group['totals']['cgst'],
                $group['totals']['igst'],
                '00',
            ];

            $columns[] = [
                "Date", "Bill No", "Qty", "Taxable", "SGST", "CGST", "IGST", "Cess"
            ];

            foreach ($group['details'] as $detail) {
                $columns[] = [
                    $detail['date'],
                    $detail['invoice_no'],
                    $detail['qty'],
                    $detail['sub_total'],
                    $detail['sgst'],
                    $detail['cgst'],
                    $detail['igst'],
                    '00',
                ];
            }

            $columns[] = [
                'Total', '',
                $group['totals']['qty'],
                $group['totals']['sub_total'],
                $group['totals']['sgst'],
                $group['totals']['cgst'],
                $group['totals']['igst'],
                '00',
            ];

            $columns[] = ['', '', '', '', '', '', '', '', ''];
        }

        return collect($columns);
    }

    public function startCell(): string
    {
        return 'A1';
    }

    public function styles(Worksheet $sheet)
    {
        if ($this->organizationName) {
            $sheet->mergeCells('A1:H1');
            $sheet->getStyle('A1:H1')->getFont()->setBold(true)->setSize(12)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
            $sheet->getStyle('A1:H1')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A1:H1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor();   
        }
    
        if ($this->fromDate && $this->toDate) {
            $sheet->mergeCells('A2:H2');
            $sheet->getStyle('A2:H2')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A2:H2')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A2:H2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor();   
        }
    
        $sheet->getStyle('A3:H3')->getFont()->setBold(true)->setSize(10)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
        $sheet->getStyle('A3:H3')->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle('A3:H3')->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle('A3:H3')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFCC');  
    
        $highestRow = $sheet->getHighestRow();
        $sheet->getStyle("A4:H$highestRow")->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle("A4:H$highestRow")->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle("A4:H$highestRow")->getFont()->setSize(10);
    
        foreach (range(4, $highestRow) as $row) {
            $rowValues = $sheet->getCell("A$row")->getValue();
            
            if (strtolower($rowValues) === 'total') {
                $sheet->getStyle("A$row:H$row")->getFont()->setBold(true);
                $sheet->getStyle("A$row:H$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('D9E1F2');   
            }
            else {
                if ($row % 2 == 0) {
                    $sheet->getStyle("A$row:H$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('F9F9F9');  
                } else {
                    $sheet->getStyle("A$row:H$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFFF');  
                }
            }
        }
    
        foreach (range('A', 'H') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }
    }
