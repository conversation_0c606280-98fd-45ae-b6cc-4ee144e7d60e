<?php

namespace App\Jobs;

use App\Models\LeadSequence;
use App\Models\Leads;
use App\Models\MailConfig;
use App\Helpers\MailHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\BulkEmail;
use Illuminate\Support\Facades\Storage;

class SendSequenceEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $leadSequenceId;
    protected $isPreview = false;

    /**
     * Create a new job instance.
     *
     * @param int $leadSequenceId
     * @param bool $isPreview
     */
    public function __construct($leadSequenceId, $isPreview = false)
    {
        $this->leadSequenceId = $leadSequenceId;
        $this->isPreview = $isPreview;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $leadSequence = LeadSequence::with(['sequence.mailConfig', 'lead'])
            ->find($this->leadSequenceId);

        if (!$leadSequence || !$leadSequence->sequence) {
            Log::error("Invalid lead sequence ID: {$this->leadSequenceId}");
            return;
        }

        $mailConfig = $leadSequence->sequence->mailConfig;

        if (!$mailConfig) {
            Log::error("Mail config not found for sequence ID: {$leadSequence->sequence->id}");
            return;
        }

        MailHelper::setMailConfig($mailConfig->id);

        try {
            //   Log::info('Converted HTML With IMG111');
            // Process dynamic content with actual lead data
            $lead = $leadSequence->lead;
            $originalContent = $leadSequence->content;

            $companyInfoHtml = $mailConfig->companyInfo;

            $finalContent = $originalContent . $companyInfoHtml;


            // Send the actual email
            Mail::to($leadSequence->lead->email)->send(new BulkEmail($leadSequence->subject, $finalContent, $leadSequence));

            // Mark the sequence completed
            $leadSequence->update([
                'status'    => 'completed',
                'sent_time' => now(),
                'full_content' => $finalContent,
            ]);

        } catch (\Throwable $e) {
            Log::error("SendSequenceEmailJob failed: " . $e->getMessage(), [
                'lead_sequence_id' => $this->leadSequenceId,
                'exception' => $e->getTraceAsString()
            ]);
            $this->fail($e);
        }
    }
}


