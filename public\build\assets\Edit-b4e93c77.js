import{T as _,o as b,c as h,a as e,u as s,w as o,F as x,Z as g,b as t,j as d,g as i,h as v}from"./app-b1cf8b3a.js";import{_ as j,b as y}from"./AdminLayout-13d09996.js";import{_ as u}from"./InputLabel-352dc786.js";import{_ as w}from"./TextInput-796570c1.js";import{_ as V}from"./TextArea-7c379bf5.js";import{_ as p}from"./InputError-841cc88b.js";import{P as k}from"./PrimaryButton-05be7944.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top"},T={class:"flex justify-between items-center mb-6"},$=t("div",null,[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Template")],-1),E={class:"flex space-x-4"},N={class:"bg-white shadow rounded-lg"},S=["onSubmit"],U={class:"flex mt-6 items-center justify-between"},q={class:"ml-auto flex items-center justify-end gap-x-6"},C=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),H={__name:"Edit",props:["template"],setup(c){const n=c,a=_({subject:n.template.subject,content:n.template.content}),f=()=>{a.patch(route("templates.update",n.template.id))};return(r,l)=>(b(),h(x,null,[e(s(g),{title:"Template"}),e(j,null,{default:o(()=>[t("div",B,[t("div",T,[$,t("div",E,[e(s(d),{href:r.route("templates.show",c.template.id),class:"text-sm text-gray-600 hover:text-gray-900"},{default:o(()=>[i(" View Template ")]),_:1},8,["href"]),e(s(d),{href:r.route("templates.index"),class:"text-sm text-gray-600 hover:text-gray-900"},{default:o(()=>[i(" ← Back to Templates ")]),_:1},8,["href"])])]),t("div",N,[t("form",{onSubmit:v(f,["prevent"]),class:"p-6 space-y-6"},[t("div",null,[e(u,{for:"subject",value:"Subject"}),e(w,{id:"subject",type:"text",class:"mt-1 block w-full",modelValue:s(a).subject,"onUpdate:modelValue":l[0]||(l[0]=m=>s(a).subject=m),required:"",autofocus:"",placeholder:"Enter template subject"},null,8,["modelValue"]),e(p,{class:"mt-2",message:s(a).errors.subject},null,8,["message"])]),t("div",null,[e(u,{for:"content",value:"Content"}),e(V,{id:"content",class:"mt-1 block w-full",modelValue:s(a).content,"onUpdate:modelValue":l[1]||(l[1]=m=>s(a).content=m),rows:"10",required:"",placeholder:"Enter template content..."},null,8,["modelValue"]),e(p,{class:"mt-2",message:s(a).errors.content},null,8,["message"])]),t("div",U,[t("div",q,[e(y,{href:r.route("templates.index")},{svg:o(()=>[C]),_:1},8,["href"]),e(k,{disabled:s(a).processing},{default:o(()=>[i("Update")]),_:1},8,["disabled"])])])],40,S)])])]),_:1})],64))}};export{H as default};
