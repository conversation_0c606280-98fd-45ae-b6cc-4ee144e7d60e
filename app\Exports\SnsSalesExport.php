<?php

namespace App\Exports;

use App\Models\SerialNumbers;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SnsSalesExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;
    protected $companyName;

    public function __construct(array $allData, $organizationName = null, $companyName = null, $fromDate = null,  $toDate = null)
    {
        $this->allData = $allData;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
        $this->companyName = $companyName;
    }

    public function title(): string
    {
        return 'SNS Sales Report as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        $headings = [];

        if ($this->organizationName) {
            $headings[] = ["Organization: " . $this->organizationName];
        }

        if ($this->companyName) {
            $headings[] = ["Company: " . $this->companyName];
        }

        if ($this->fromDate && $this->toDate) {
            $headings[] = ["From Date: " . $this->fromDate . " To Date: " . $this->toDate];
        }

        $headings[] = [
            "Description",
            "Qty",
        ];

        return $headings;
    }

    public function collection()
    {
        $data = $this->allData;
        $rows = [];

        foreach ($data as $product) {
            $productCode = $product['item_code'];
            $productName = $product['name'];
            $totalQty = 0;

            $rows[] = [
                'Description' => $productCode.'  '.$productName,
                'Qty' => '',
            ];

            if (isset($product['invoice_details']) && is_array($product['invoice_details'])) {
                foreach ($product['invoice_details'] as $invoiceDetail) {
                    $customerName = $invoiceDetail['invoice']['customers']['customer_name'] ?? 'Unknown Customer';
                    $qty = $invoiceDetail['qty'] ?? 0;
                    $rows[] = [
                        'Description' => ' - ' . $customerName,
                        'Qty' => $qty,
                    ];
                    $totalQty += $qty;
                }
            }

            $rows[] = [
                'Description' => 'Total',
                'Qty' => $totalQty,
            ];
        }

        return collect($rows);
    }

    public function startCell(): string
    {
        return 'A1';
    }

    public function styles(Worksheet $sheet)
    {
        if ($this->organizationName) {
            $sheet->mergeCells('A1:B1');
            $sheet->getStyle('A1:B1')->getFont()->setBold(true)->setSize(12)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
            $sheet->getStyle('A1:B1')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A1:B1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }
    
        if ($this->companyName) {
            $sheet->mergeCells('A2:B2');
            $sheet->getStyle('A2:B2')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A2:B2')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A2:B2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }
    
        if ($this->fromDate && $this->toDate) {
            $sheet->mergeCells('A3:B3');
            $sheet->getStyle('A3:B3')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A3:B3')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A3:B3')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }
    
        $sheet->getStyle('A4:B4')->getFont()->setBold(true)->setSize(10)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
        $sheet->getStyle('A4:B4')->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle('A4:B4')->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle('A4:B4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFCC');
    
        $highestRow = $sheet->getHighestRow();
        $sheet->getStyle("A5:B$highestRow")->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle("A5:B$highestRow")->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle("A5:B$highestRow")->getFont()->setSize(10);
    
        foreach (range(5, $highestRow) as $row) {
            $rowValues = $sheet->getCell("A$row")->getValue();
    
            if (strtolower($rowValues) === 'total') {
                $sheet->getStyle("A$row:B$row")->getFont()->setBold(true);
                $sheet->getStyle("A$row:B$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('D9E1F2');
            } else {
                if ($row % 2 == 0) {
                    $sheet->getStyle("A$row:B$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('F9F9F9');
                } else {
                    $sheet->getStyle("A$row:B$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFFF');
                }
            }
        }
    
        foreach (range('A', 'B') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }
}

