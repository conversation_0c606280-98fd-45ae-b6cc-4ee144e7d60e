<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\DB;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        if (DB::table('roles')->count() == 0) {
            DB::table('roles')->insert([
                [
                    'name'  => 'Admin',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Accountant',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Sales',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'Service',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'  => 'HR',
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);
        }

        if (DB::table('email_tags')->count() == 0) {
            DB::table('email_tags')->insert([
                [
                    'name'  => '{first_name}',
                    'description' => 'First name',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{last_name}',
                    'description'=> 'Last name',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{country}',
                    'description'=> 'Country',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{city}',
                    'description'=> 'City',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{designation}',
                    'description'=> 'Designation',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{email}',
                    'description'=> 'Email',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{organization_name}',
                    'description'=> 'Organization Name',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{organization_website_url}',
                    'description'=> 'Organization website URL',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{linkedin_url}',
                    'description'=> 'Linkedin URL',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],

                [
                    'name'=> '{organization_linkedin_url}',
                    'description'=> 'Organization Linkedin URLL',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name'=> '{signature}',
                    'description'=> 'Signature',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);
        }

        // Use direct database approach to avoid the assignRole() bug
        if (DB::table('roles')->count() > 0 && DB::table('permissions')->count() > 0) {

            foreach (User::all() as $user) {
                if ($user->role_id) {
                    $role = Role::find($user->role_id);
                    if ($role) {
                        echo "Assigning role '{$role->name}' to user {$user->id}\n";

                        // Use direct database insertion instead of assignRole()
                        DB::table('model_has_roles')->updateOrInsert([
                            'role_id' => $role->id,
                            'model_type' => 'App\\Models\\User',
                            'model_id' => $user->id
                        ]);

                        echo "Successfully assigned role to user {$user->id}\n";
                    }
                }
            }

            // Assign permissions to Admin role
            $permissions = Permission::all();
            $adminRole = Role::where('name', 'Admin')->first();

            if ($adminRole && $permissions->count() > 0) {
                try {
                    $adminRole->givePermissionTo($permissions);
                    echo "Successfully assigned permissions to Admin role\n";
                } catch (\Exception $e) {
                    echo "Error assigning permissions: " . $e->getMessage() . "\n";
                }
            }
        }

        echo "Seeder completed successfully\n";
    }
}
