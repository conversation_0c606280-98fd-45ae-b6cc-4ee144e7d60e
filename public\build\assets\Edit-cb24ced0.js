import{_ as o}from"./AdminLayout-13d09996.js";import i from"./DeleteUserForm-7498788e.js";import m from"./UpdatePasswordForm-9a16ccc1.js";import r from"./UpdateProfileInformationForm-f74eb060.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-b1cf8b3a.js";import"./DangerButton-304eb6b1.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-841cc88b.js";import"./InputLabel-352dc786.js";import"./Modal-9f7e750d.js";/* empty css                                                              */import"./SecondaryButton-5ec31741.js";import"./TextInput-796570c1.js";import"./PrimaryButton-05be7944.js";import"./TextArea-7c379bf5.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
