<script setup>
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SelectionModal from '@/Components/SelectionModal.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import MultipleCheckbox from '@/Components/MultipleCheckbox.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';


const props = defineProps({
  data: Object,
  countries: Array,
  cities: Array,
  sequences: Array,
  existingInSequence: Array,
  allLeadIds: Array,
  filters: Object, // ← New
});

const searchValue = ref(props.filters?.search || '');
const country = ref(props.filters?.country || '');
const city = ref(props.filters?.city || '');
const sequence = ref(props.filters?.sequence || '');
const assignmentStatus = ref(props.filters?.assignment_status || 'unassigned');

const form = useForm({});

const modalVisible = ref(false);
const selectedId = ref(null);
const showSequenceModal = ref(false);
const showSelectionModal = ref(false);
const selectedSequence = ref(null);
const headerCheckboxState = ref(false);

// Selected leads array
const selectedLeads = ref([]);

onMounted(() => {
  const savedSelectedLeads = localStorage.getItem('selectedLeads');
  if (savedSelectedLeads) {
    const parsedLeads = JSON.parse(savedSelectedLeads);
    // Filter leads to only include those present in the current query's allLeadIds
    const filteredLeads = parsedLeads.filter(id => props.allLeadIds.includes(id));
    selectedLeads.value = filteredLeads;
    // Update localStorage to remove any stale IDs
    localStorage.setItem('selectedLeads', JSON.stringify(filteredLeads));
  }
});

watch(() => props.allLeadIds, () => {
  selectedLeads.value = selectedLeads.value.filter(id => props.allLeadIds.includes(id));
  persistSelectedLeads();
});

// Computed property to determine whether any lead is selected.
const anySelected = computed(() => selectedLeads.value.length > 0);


// Check if lead is already in the selected sequence
const isLeadInSequence = id => props.existingInSequence.includes(id);


const eligibleAllPages = computed(() =>
  props.allLeadIds.filter(id => !isLeadInSequence(id))
);

// Open the selection modal when the header checkbox is clicked
const toggleSelectAll = () => {
  showSelectionModal.value = true;
};

// Handle selection based on modal choice
const handleSelectionApply = (selectionType) => {
  if (selectionType === 'current') {
    // Select only current page eligible leads
    const currentPageEligibleLeads = props.data.data
      .filter(lead => !isLeadInSequence(lead.id))
      .map(lead => lead.id);

    selectedLeads.value = [...new Set([...selectedLeads.value, ...currentPageEligibleLeads])];

    // Update header checkbox state
    headerCheckboxState.value = true;
  } else if (selectionType === 'all') {
    // Select all eligible leads across all pages
    selectedLeads.value = [...new Set([...selectedLeads.value, ...eligibleAllPages.value])];

    // Update header checkbox state
    headerCheckboxState.value = true;
  }
};

// Handle cancel action - deselect all checkboxes
const handleCancel = () => {
  // Clear all selections
  selectedLeads.value = [];
  localStorage.removeItem('selectedLeads');

  // Update header checkbox state
  headerCheckboxState.value = false;
};

// Reference to the select all checkbox
const selectAllRef = ref(null);

// Update indeterminate state and header checkbox state based on selection
watch(selectedLeads, () => {
  const cb = selectAllRef.value;
  if (cb) {
    // Set indeterminate state
    cb.indeterminate =
      selectedLeads.value.length > 0 &&
      selectedLeads.value.length < eligibleAllPages.value.length;

    // Update header checkbox state based on selection
    if (eligibleAllPages.value.length > 0 && eligibleAllPages.value.every(id => selectedLeads.value.includes(id))) {
      headerCheckboxState.value = true;
    } else if (selectedLeads.value.length === 0) {
      headerCheckboxState.value = false;
    }
  }
});

// Computed property for current page eligible leads count
const currentPageEligibleCount = computed(() => {
  return props.data.data.filter(lead => !isLeadInSequence(lead.id)).length;
});

// Removed isAllSelected computed property as we now use headerCheckboxState

// Check if all leads are in sequence
const allLeadsInSequence = computed(() => {
  return props.data.data.length > 0 &&
         props.data.data.every(lead => isLeadInSequence(lead.id));
})

const createLeadSequence = () => {
  if (!selectedSequence.value) return;

  router.post(route('lead-sequence'), {
    sequence_id: selectedSequence.value,
    lead_ids: selectedLeads.value
  }, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      selectedLeads.value = [];
      selectedSequence.value = null;
      showSequenceModal.value = false;
      toggleSelectAll.value = false;
      closeMultiBox ();
    }
  });
};

// No longer needed as we're using direct binding

const closeMultiBox = () => {
  selectedLeads.value = [];
  localStorage.removeItem('selectedLeads');
  headerCheckboxState.value = false;
};

// Add method to open sequence modal
const openSequenceModal = () => {
    showSequenceModal.value = true;
};

const closeSequenceModal = () => {
    showSequenceModal.value = false;
    selectedSequence.value = null;
};

const openDeleteModal = (Id) => {
    selectedId.value = Id;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('leads.destroy', selectedId.value), {
        onSuccess: () => closeModal()
    });
};

const handleSearchChange = (searchVal, countryVal, cityVal, sequenceVal, assignmentStatusVal) => {
    searchValue.value = searchVal;
    country.value = countryVal;
    city.value = cityVal;
    sequence.value = sequenceVal;
    assignmentStatus.value = assignmentStatusVal || 'unassigned'; // Default to unassigned

    form.get(route('leads.index', {
        search: searchVal,
        country: countryVal,
        city: cityVal,
        sequence: sequenceVal,
        assignment_status: assignmentStatus.value
    }), {
        preserveState: true,
        replace: true
    });
};

const setCountry = (value) => {
    country.value = value;
    handleSearchChange(searchValue.value, value, city.value, sequence.value, assignmentStatus.value);
};

const setCity = (value) => {
    city.value = value;
    handleSearchChange(searchValue.value, country.value, value, sequence.value, assignmentStatus.value);
}

const setSequence = (id) => {
    sequence.value = id;
    selectedLeads.value = [];
    handleSearchChange(searchValue.value, country.value, city.value, id, assignmentStatus.value);
};

const setAssignmentStatus = (status) => {
    assignmentStatus.value = status;
    selectedLeads.value = [];
    handleSearchChange(searchValue.value, country.value, city.value, sequence.value, status);
};


const changeEvent = (event) => {
    console.log(event);
};

const persistSelectedLeads = () => {
  localStorage.setItem('selectedLeads', JSON.stringify(selectedLeads.value));
};

watch(selectedLeads, persistSelectedLeads, { deep: true });


const getUniqueSequences = (leadSequences) => {
    return [...new Map(leadSequences.map(ls =>
        [ls.sequence.id, ls.sequence]
    )).values()];
};

</script>

<template>
    <Head title="Leads" />

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Leads</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field" @input="handleSearchChange($event.target.value, country, city, sequence)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                        <div class="flex justify-end">
                            <CreateButton :href="route('leads.create')">
                                    Add Leads
                            </CreateButton>
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                        <div class="flex justify-end">
                            <CreateButton :href="route('uploadXls')">
                                Upload XLS
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                      </svg>
                      <InputLabel for="customer_id" value="Filters" class="ml-2" />
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-3">
                        <InputLabel for="customer_id" value="Country" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew
                            :options="[ { id: '', name: 'All Country' }, ...countries.map(country => ({ id: country, name: country })) ]"
                            v-model="country"
                            @onchange="setCountry"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-3">
                        <InputLabel for="customer_id" value="City" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew
                            :options="[ { id: '', name: 'All City' }, ...cities.map(city => ({ id: city, name: city })) ]"
                            v-model="city"
                            @onchange="setCity"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-3">
                        <InputLabel for="customer_id" value="Sequence" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew
                                :options="[{ id: '', name: 'All Sequence' }, ...sequences]"
                                v-model="sequence"
                                @onchange="setSequence"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-3">
                        <InputLabel for="assignment_status" value="Assignment Status" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew
                                :options="[
                                    { id: 'all', name: 'All Leads' },
                                    { id: 'assigned', name: 'Assigned Leads' },
                                    { id: 'unassigned', name: 'Unassigned Leads' }
                                ]"
                                v-model="assignmentStatus"
                                @onchange="setAssignmentStatus"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th scope="col" class="px-4">
                                    <input type="checkbox" v-if="!allLeadsInSequence" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                        ref="selectAllRef"
                                        v-model="headerCheckboxState"
                                        @click.prevent="toggleSelectAll"
                                    />
                                  </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                NAME
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                COUNTRY
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                CITY
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                DESIGNATION
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                Email
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                    SEQUENCES
                                </th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                 ACTION
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && data.data.length > 0">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="userData in data.data" :key="userData.id">

                                <td class="">
                                    <div class="pl-3">
                                      <div v-if="!isLeadInSequence(userData.id)">
                                            <MultipleCheckbox
                                                v-model:checked="selectedLeads"
                                                :value="userData.id"
                                                @change="changeEvent"
                                            />
                                      </div>
                                    </div>
                                </td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">
                                    {{ userData.first_name }} {{ userData.last_name }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ userData.country }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ userData.city }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ userData.designation }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ userData.email }}
                                </td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">
                                    <template v-if="userData.lead_sequences.length > 0">
                                        <div v-for="sequence in getUniqueSequences(userData.lead_sequences)" :key="sequence.id">
                                            {{ sequence.name?? '-' }}
                                        </div>
                                    </template>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('leads.edit',{id:userData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button" @click="openDeleteModal(userData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="showSequenceModal" @close="closeSequenceModal">
            <div class="p-6">
              <h2 class="text-lg font-medium text-gray-900 mb-4">
                    Select a Sequence for {{ selectedLeads.length }} Leads
              </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    <div v-for="sequence in sequences" :key="sequence.id" @click="selectedSequence = selectedSequence === sequence.id ? null : sequence.id"
                        :class="['p-4 border rounded cursor-pointer transition-colors flex justify-between items-center', selectedSequence === sequence.id ? 'bg-green-100 border-green-500' : 'bg-white border-gray-200 hover:bg-gray-50']">
                        <span class="truncate" :class="selectedSequence === sequence.id ? 'text-green-600' : 'text-gray-800'">
                            {{ sequence.name }}
                        </span>
                    </div>
                </div>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeSequenceModal">
                        Cancel
                    </SecondaryButton>
                    <div class="w-36">
                        <CreateButton class="ml-3 w-20" @click="createLeadSequence" :disabled="!selectedSequence">
                            Create
                        </CreateButton>
                    </div>
                </div>
            </div>
        </Modal>
        <SelectionModal
            :show="showSelectionModal"
            @close="showSelectionModal = false"
            @apply="handleSelectionApply"
            @cancel="handleCancel"
            :current-page-count="currentPageEligibleCount"
            :total-count="eligibleAllPages.length"
        />
        <div v-if="anySelected" class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-50">
            <div class="max-w-7xl mx-auto relative flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">
                        {{ selectedLeads.length }} selected
                    </span>
                    <button @click="closeMultiBox" class="text-gray-400 hover:text-red-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                <div class="absolute left-1/2 transform -translate-x-1/2">
                    <PrimaryButton @click="openSequenceModal">
                        Select Sequence
                    </PrimaryButton>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<style scoped>
.sequence-item {
  transition: all 0.2s ease;
}
.fixed {
    transition: transform 0.3s ease;
  }
</style>
