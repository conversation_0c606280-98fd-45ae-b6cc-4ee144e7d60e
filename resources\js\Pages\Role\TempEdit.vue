<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { Head , Link, useForm, usePage } from '@inertiajs/vue3';

const props = defineProps(['data']);

const userData = usePage().props.data[0];

const form = useForm({
    customer_id: userData.customer_id,
    id: userData.id,
    product_code: userData.product_code,
    product_name:userData.product_name,
    serial_no:userData.serial_no
});

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

</script>

<template>
    <Head title="Edit Report" />
    <AdminLayout>
        <div class="h-screen">
        <div class="animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900">Edit Report</h2>
            <form @submit.prevent="form.patch(route('service-reports.update'))">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10">
                        <div class="sm:col-span-3">
                            <InputLabel for="product_code" value="Product Code" />
                            <TextInput
                                id="product_code"
                                type="text"
                                @change="clearError('data.product_code')"
                                v-model="form.product_code"
                                :class="{ 'error rounded-md': form.errors[`data.product_code`] }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product_name" value="Product Name" />
                            <TextInput
                                id="product_name"
                                type="text"
                                @change="clearError('data.product_name')"
                                v-model="form.product_name"
                                :class="{ 'error rounded-md': form.errors[`data.product_name`] }"
                            />
                            <InputError class="" :message="form.errors.product_name" />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="serial_no" value="Serial No" />
                            <TextInput
                                id="serial_no"
                                type="text"
                                @change="clearError('data.serial_no')"
                                v-model="form.serial_no"
                                :class="{ 'error rounded-md': form.errors[`data.serial_no`] }"
                            />
                            <InputError  class="" :message="form.errors.serial_no" />
                        </div>
                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('service-reports.show',{id:userData.customer_id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>




                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>
