<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maat<PERSON>bsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class MaintenanceExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithCustomStartCell, ShouldAutoSize
{
    protected $data;
    protected $sNo = 1;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data->filter(function ($item) {
            return $item->status !== 'Close';
        });
    }

    public function headings(): array
    {
        return [
            'SNo',
            'Hospital Name',
            'Product',
            'Price (₹)',
            'PM Date 1',
            'PM Date 2',
            'PM Date 3',
            'PM Date 4',
            'Contract Start Date',
            'Contract End Date',
            'Status',
            'Type',
        ];
    }

    public function map($item): array
    {
        return [
            $this->sNo++,
            $item->hospital_name,
            $item->product_name,
            $item->price,
            $item->pm_date_1,
            $item->pm_date_2,
            $item->pm_date_3,
            $item->pm_date_4,
            $item->contract_start_date,
            $item->contract_end_date,
            $item->status,
            $item->maintenance_type,
        ];
    }

    public function startCell(): string
    {
        return 'A5';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:L1');
        $sheet->mergeCells('A2:L2');
        $sheet->setCellValue('A1', 'MAINTENANCE CONTRACT EXPORT');
        $sheet->setCellValue('A2', 'Report Date: ' . now()->format('Y-m-d'));

        $styleArray = [
            'font' => [
                'bold' => true,
                'size' => 14,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet->getStyle('A1')->applyFromArray($styleArray);
        $sheet->getStyle('A2')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
        ]);

        $sheet->getStyle('A5:L5')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'CCCCCC',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);

        $currentMonth = now()->month;
        $currentYear = now()->year;
        $lastRow = 5 + $this->data->count();

        for ($row = 6; $row <= $lastRow; $row++) {
            $sheet->getStyle("D{$row}")->getNumberFormat()->setFormatCode('#,##0.00'); // This will format the number as currency
            $sheet->getStyle("D{$row}")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER); // Center align the Price column

            $highlightRow = false;

            for ($col = 5; $col <= 8; $col++) {
                $dateValue = $sheet->getCellByColumnAndRow($col, $row)->getValue();
                if ($dateValue && Carbon::parse($dateValue)->month == $currentMonth && Carbon::parse($dateValue)->year == $currentYear) {
                    $highlightRow = true;
                    break;
                }
            }

            if ($highlightRow) {
                $sheet->getStyle("A{$row}:L{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'FFFFCC',
                        ],
                    ],
                    'font' => [
                        'color' => ['rgb' => 'FFCC00'],
                    ],
                ]);
            }

            $maintenanceType = $sheet->getCell("L{$row}")->getValue();
            if ($maintenanceType === 'AMC') {
                $sheet->getStyle("L{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'B2EBF2', // Light cyan background for AMC
                        ],
                    ],
                    'font' => [
                        'color' => ['rgb' => '00838F'], // Dark cyan text color for AMC
                    ],
                ]);
            } elseif ($maintenanceType === 'CMC') {
                $sheet->getStyle("L{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'D4EDDA', // Light green background for CMC
                        ],
                    ],
                    'font' => [
                        'color' => ['rgb' => '155724'], // Dark green text color for CMC
                    ],
                ]);
            }
        }

        return [];
    }
}
