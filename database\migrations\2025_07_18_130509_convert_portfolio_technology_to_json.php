<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if technology column exists, if not add it
        if (!DB::getSchemaBuilder()->hasColumn('portfolios', 'technology')) {
            DB::statement('ALTER TABLE portfolios ADD COLUMN technology TEXT NULL');
        }

        // Convert existing text data to JSON array and update in place
        $portfolios = DB::table('portfolios')->get();
        foreach ($portfolios as $portfolio) {
            if ($portfolio->technology) {
                // Split by comma and clean up the values
                $technologies = array_map('trim', explode(',', $portfolio->technology));
                $technologies = array_filter($technologies); // Remove empty values

                DB::table('portfolios')
                    ->where('id', $portfolio->id)
                    ->update(['technology' => json_encode($technologies)]);
            } else {
                // Set empty array for null values
                DB::table('portfolios')
                    ->where('id', $portfolio->id)
                    ->update(['technology' => json_encode([])]);
            }
        }

        // Now change the column type to JSON
        DB::statement('ALTER TABLE portfolios MODIFY COLUMN technology JSON');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert JSON back to text
        $portfolios = DB::table('portfolios')->get();
        foreach ($portfolios as $portfolio) {
            if ($portfolio->technology) {
                $technologies = json_decode($portfolio->technology, true);
                if (is_array($technologies)) {
                    $technologyText = implode(', ', $technologies);
                    DB::table('portfolios')
                        ->where('id', $portfolio->id)
                        ->update(['technology' => $technologyText]);
                }
            }
        }

        // Change column type back to TEXT
        DB::statement('ALTER TABLE portfolios MODIFY COLUMN technology TEXT');
    }
};
