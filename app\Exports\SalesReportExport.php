<?php 
namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class SalesReportExport implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles
{
    protected $salesData;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;

    public function __construct($salesData, $organizationName, $fromDate, $toDate)
    {
        $this->salesData = $salesData;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
    }

    public function headings(): array
    {
        return [
            [$this->organizationName], 
            ["From: {$this->fromDate} To: {$this->toDate}"], 
            ['Sno', 'Invoice Number', 'TYPE', 'CATEGORY', 'CUSTOMER NAME', 'DATE', 'AMOUNT (₹)'],   
        ];
    }

    public function collection()
    {
        $data = [];
        $totalAmount = 0;

        foreach ($this->salesData as $index => $sale) {
            $amount = $sale->total_amount ?? 0;
            $totalAmount += $amount;

            $data[] = [
                'Sno'            => $index + 1,
                'Invoice Number' => $sale->invoice_no ?? '-',
                'Type'           => $sale->invoice_type ?? '-',
                'Category'       => $sale->category ?? '-',
                'Customer Name'  => $sale->customers->customer_name ?? '-',
                'Date'           => Carbon::parse($sale->date)->format('M d, Y') ?? '-',
                'Amount (₹)'         => number_format($amount, 2),
            ];
        }

        $data[] = [
            'Sno'            => 'TOTAL',
            'Invoice Number' => '',
            'Type'           => '',
            'Category'       => '',
            'Customer Name'  => '',
            'Date'           => '',
            'Amount (₹)'         => number_format($totalAmount, 2),
        ];

        return collect($data);
    }

    public function columnFormats(): array
    {
        return [
            'E' => NumberFormat::FORMAT_DATE_XLSX15, 
            'G' => NumberFormat::FORMAT_NUMBER_00, 
        ];
    }

    public function styles($sheet)
    {
        $rowCount = count($this->salesData) + 4; 
    
        $sheet->mergeCells('A1:G1'); 
        $sheet->mergeCells('A2:G2'); 
    
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
        $sheet->getStyle('A3:G3')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A3:G3')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFFFCC');
        $sheet->getStyle('A3:G3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
        $sheet->getStyle("A3:G{$rowCount}")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);
    
        $sheet->getStyle("A4:G{$rowCount}")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);
    
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    
        $sheet->getStyle("A{$rowCount}:G{$rowCount}")->getFont()->setBold(true);
        $sheet->getStyle("A{$rowCount}:G{$rowCount}")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('D9E1F2');
        $sheet->getStyle("A{$rowCount}:G{$rowCount}")->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

            foreach (range(4, $rowCount - 1) as $row) {
                if ($row % 2 == 0) {
                    $sheet->getStyle("A$row:G$row")->getFill()
                        ->setFillType(Fill::FILL_SOLID)
                        ->getStartColor()->setRGB('F9F9F9');
                } else {
                    $sheet->getStyle("A$row:G$row")->getFill()
                        ->setFillType(Fill::FILL_SOLID)
                        ->getStartColor()->setRGB('FFFFFF');
                }
            }    

        return $sheet;
    }
    }
