<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Webklex\IMAP\Facades\Client;
use Illuminate\Support\Facades\Log;
use App\Models\LeadSequence;

class CheckEmailBounces extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:check-bounces';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check IMAP inbox for bounce emails and update LeadSequence status';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $client = Client::account('default');
            $client->connect();
            if ($client->isConnected()) {
                Log::info('IMAP connection successful.');

                // Access the INBOX folder
                $folder = $client->getFolder('INBOX');

                // Fetch recent messages (e.g., last 2 days)
                $messages = $folder->query()->since(now()->subDays(2))->get();

                $this->info("Found {$messages->count()} messages.");
                Log::info("Retrieved {$messages->count()} messages from INBOX.");

                foreach ($messages as $message) {
                    $subject = $message->getSubject();
                    $body = $message->getTextBody();

                    if(str_contains($subject, 'Delivery Status Notification (Failure)')){
                        preg_match_all('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $body, $matches);
                        $failedEmail = array_unique($matches[0]);

                        if ($failedEmail) {
                            $dateRaw = $message->getDate();
                            $date = $dateRaw instanceof \Carbon\Carbon ? $dateRaw->format('Y-m-d H:i:s') : (string) $dateRaw;
                            Log::info('Email received', [
                                'failedEmail' => $failedEmail,
                                'date' => $date,
                            ]);
                            $sequences = LeadSequence::whereHas('lead', function ($q) use ($failedEmail) {
                                $q->where('email', $failedEmail);
                            })->where('status', 'completed')->get();
                            foreach ($sequences as $seq) {
                                $seq->update([
                                    'status' => 'failed',
                                    'failure_reason' => 'Bounced email',
                                    'last_error' => 'Bounced email',
                                    'attempts' => 0,
                                    'rescheduled_at' => $date,
                                ]);
                            }
                        }
                    }
                }
            } else {
                Log::warning('IMAP connection failed.');
            }
            $client->disconnect();
        } catch (\Throwable $e) {
            Log::error('IMAP connection error: ' . $e->getMessage());
        }
        $this->info("Bounce check complete.");
    }
}
