<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class UserCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param Request $request
     * @return array<int|string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($data) {
                return [
                    'id' => $data->id,
                    'first_name' => $data->first_name,
                    'last_name' => $data->last_name,
                    'email' => $data->email,
                    'role' => $data->roles->name,
                    'status' => $data->status
                ];
            })
        ];
    }

    /**
     * Get the URL for a given page number.
     *
     * @param int $page
     * @return string|null
     */

    private function urlForPage(int $page): ?string
    {
        return $this->url($page);
    }
}
