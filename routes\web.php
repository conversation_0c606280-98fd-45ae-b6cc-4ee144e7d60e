<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LeadsController;
use App\Http\Controllers\SequencesController;
use App\Http\Controllers\SequenceStepController;
use App\Http\Controllers\MailConfigController;
use App\Http\Controllers\EmailTagController;
use App\Http\Controllers\LeadSequenceController;
use App\Http\Controllers\EmailTrackingController;
use App\Http\Controllers\ProspectController;
use App\Http\Controllers\PortfolioController;
use App\Http\Controllers\TemplateController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Inertia::render('Auth/Login', [
        'canLogin'          => Route::has('login'),
        'canRegister'       => Route::has('register'),
        'laravelVersion'    => Application::VERSION,
        'phpVersion'        => PHP_VERSION,
    ]);
});

Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

Route::get('/email/open/{id}', [EmailTrackingController::class, 'trackOpen'])
     ->name('email.open');

Route::get('/email/click/{id}', [EmailTrackingController::class, 'trackClick'])
     ->name('email.click');

     Route::get('/email/signature/{id}', [EmailTrackingController::class, 'trackSignature'])
     ->name('email.signature');


Route::middleware('auth')->group(function () {

    Route::get('/profile',      [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile',    [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile',   [ProfileController::class, 'destroy'])->name('profile.destroy');

    //Roles
    Route::resource('roles', RoleController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Users
    Route::resource('users', UserController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/users',              [UserController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('users.update');
    Route::post('/users/activation',    [UserController::class, 'activation'])->name('users.activation');
    Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('users.destroy');

    //Leads
    Route::resource('leads', LeadsController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/leads', [LeadsController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('leads.update');
    Route::delete('/leads/{id}', [LeadsController::class, 'destroy'])->name('leads.destroy');
    Route::get('/upload-xls', [LeadsController::class, 'uploadXls'])->name('uploadXls');
    Route::post('/leads/import-xls', [LeadsController::class, 'importXls'])->name('leads.importXls');
    Route::get('/sample-xls', [LeadsController::class, 'downloadSampleXls'])->name('download-sample-xls');

    //Prospects
    Route::resource('prospects', ProspectController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/prospects/{id}', [ProspectController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('prospects.update');
    Route::delete('/prospects/{id}', [ProspectController::class, 'destroy'])->name('prospects.destroy');
    Route::get('/prospects-dashboard', [ProspectController::class, 'dashboard'])->name('prospects.dashboard');
    Route::post('/prospects/{id}/activity', [ProspectController::class, 'addActivity'])->name('prospects.addActivity');
    Route::post('/prospects/{id}/convert', [ProspectController::class, 'convertToLead'])->name('prospects.convert');
    Route::patch('/prospects/{id}/status', [ProspectController::class, 'updateStatus'])->name('prospects.updateStatus');
    Route::post('/prospects/{id}/follow-up', [ProspectController::class, 'scheduleFollowUp'])->name('prospects.scheduleFollowUp');

    //Portfolio
    Route::resource('portfolios', PortfolioController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Templates
    Route::resource('templates', TemplateController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Email Sequences
    Route::resource('email-sequence', SequencesController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/email-sequence', [SequencesController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('email-sequence.update');
    Route::post('/email-sequence/activation',    [SequencesController::class, 'activation'])->name('email-sequence.activation');

    //Email Sequence Steps
    Route::resource('sequence-step', SequenceStepController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/sequence-step',               [SequenceStepController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('email-sequence-step.update');
    Route::get('/sequence-step/{id}',            [SequenceStepController::class, 'show'])->name('email-sequence-step.show');

    //smtp
    Route::resource('smtp', MailConfigController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/smtp', [MailConfigController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('smtp.update');
    Route::post('/smtp/test-mail/{id}', [MailConfigController::class, 'sendTestMail'])->name('smtp.sendTestMail');

    //Email Tags
    Route::resource('email-tag', EmailTagController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/email-tag', [EmailTagController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('email-tag.update');

    //Setting
    Route::get('/setting',[SettingController::class, 'index'])->name('setting');
    Route::get('/send-bulk-email', [LeadsController::class, 'sendBulkEmails']);

    //Lead Sequences
    Route::resource('sent-email', LeadSequenceController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('sent-email', [LeadSequenceController::class,'update'])->middleware([HandlePrecognitiveRequests::class])->name('sent-email.update');
    Route::post('/lead-sequence', [LeadSequenceController::class, 'assignLeadToSequence'])->name('lead-sequence');
    Route::post('/send-scheduled-email', [LeadSequenceController::class, 'sendScheduledEmails'])->name('send-scheduled-email');
    Route::get('/email-preview/{id}', [LeadSequenceController::class, 'preview'])->name('email.preview');

    Route::get('logs',              [SettingController::class, 'getAllLogs'])->name('logs');
    Route::get('/logs/load-more',   [SettingController::class, 'loadMoreLogs'])->name('logs.loadMore');

    // Failed Emails Management
    Route::get('/failed-emails', [LeadSequenceController::class, 'failedEmails'])->name('failed-emails.index');
    Route::patch('/failed-emails/update', [LeadSequenceController::class, 'updateFailed'])->middleware([HandlePrecognitiveRequests::class])->name('failed-emails.update');
    Route::post('/failed-emails/reschedule/{id}', [LeadSequenceController::class, 'rescheduleEmail'])->name('failed-emails.reschedule');
});


require __DIR__ . '/auth.php';
