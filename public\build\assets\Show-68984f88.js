import{_ as h}from"./AdminLayout-13d09996.js";import{o,c as s,a as n,u as l,w as d,F as m,Z as f,b as t,j as g,g as r,t as i,f as a}from"./app-b1cf8b3a.js";const _={class:"items-start"},x={class:"flex justify-between items-center mb-6"},u=t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Project Details",-1),y={class:"flex space-x-2"},p={class:"bg-white rounded-lg shadow overflow-hidden"},b={class:"px-6 py-4 border-b border-gray-200"},v={class:"flex justify-between items-start"},w={class:"text-xl font-semibold text-gray-900"},k={class:"text-sm text-gray-500 mt-1"},j={key:0,class:"text-sm text-gray-500"},D={key:0,class:"flex-shrink-0"},L=["href"],S=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),P={class:"px-6 py-6"},C={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},B={class:"lg:col-span-2 space-y-6"},U={key:0},V=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Description",-1),N={class:"max-w-none"},F={class:"text-sm text-gray-700 leading-relaxed whitespace-pre-wrap"},M={key:1},T=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Technologies Used",-1),E={class:"max-w-none"},H={class:"text-sm text-gray-700 leading-relaxed whitespace-pre-wrap"},I={key:2},R=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Login Credentials",-1),Z={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-2"},$={key:0,class:"flex justify-between items-center"},q=t("span",{class:"text-sm font-medium text-gray-700"},"Login ID:",-1),z={class:"text-sm text-gray-900 font-mono"},A={key:1,class:"flex justify-between items-center"},G=t("span",{class:"text-sm font-medium text-gray-700"},"Password:",-1),J={class:"text-sm text-gray-900 font-mono"},K={class:"lg:col-span-1"},O={class:"bg-gray-50 rounded-lg p-4 space-y-4"},Q=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Project Details",-1),W={key:0},X=t("dt",{class:"text-sm font-medium text-gray-500"},"Project URL",-1),Y={class:"mt-1"},tt=["href"],et=t("dt",{class:"text-sm font-medium text-gray-500"},"Created",-1),ot={class:"mt-1 text-sm text-gray-900"},nt={__name:"Show",props:["portfolio"],setup(e){return(c,st)=>(o(),s(m,null,[n(l(f),{title:"Portfolio"}),n(h,null,{default:d(()=>[t("div",_,[t("div",x,[u,t("div",y,[n(l(g),{href:c.route("portfolios.index"),class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"},{default:d(()=>[r(" Back to Portfolio ")]),_:1},8,["href"])])]),t("div",p,[t("div",b,[t("div",v,[t("div",null,[t("h2",w,i(e.portfolio.project_name),1),t("p",k," Created on "+i(new Date(e.portfolio.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})),1),e.portfolio.updated_at!==e.portfolio.created_at?(o(),s("p",j," Last updated on "+i(new Date(e.portfolio.updated_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})),1)):a("",!0)]),e.portfolio.url?(o(),s("div",D,[t("a",{href:e.portfolio.url,target:"_blank",class:"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors"},[S,r(" View Live Project ")],8,L)])):a("",!0)])]),t("div",P,[t("div",C,[t("div",B,[e.portfolio.description?(o(),s("div",U,[V,t("div",N,[t("p",F,i(e.portfolio.description),1)])])):a("",!0),e.portfolio.technology?(o(),s("div",M,[T,t("div",E,[t("p",H,i(e.portfolio.technology.join(", ")),1)])])):a("",!0),e.portfolio.login_id||e.portfolio.password?(o(),s("div",I,[R,t("div",Z,[e.portfolio.login_id?(o(),s("div",$,[q,t("span",z,i(e.portfolio.login_id),1)])):a("",!0),e.portfolio.password?(o(),s("div",A,[G,t("span",J,i(e.portfolio.password),1)])):a("",!0)])])):a("",!0)]),t("div",K,[t("div",O,[Q,e.portfolio.url?(o(),s("div",W,[X,t("dd",Y,[t("a",{href:e.portfolio.url,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm break-all"},i(e.portfolio.url),9,tt)])])):a("",!0),t("div",null,[et,t("dd",ot,i(new Date(e.portfolio.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})),1)])])])])])])])]),_:1})],64))}};export{nt as default};
