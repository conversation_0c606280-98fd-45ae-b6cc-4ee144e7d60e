<script setup>
import { ref, defineProps } from 'vue';
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, useForm } from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';

const props = defineProps({
  technologies: Array,
});

const form = useForm({
    project_name: '',
    url: '',
    description: '',
    technology: [],
    login_id: '',
    password: '',
});

const submit = () => {
    form.post(route('portfolios.store'), {
        onSuccess: () => form.reset(),
    });
};
</script>

<template>
    <Head title="Portfolio" />

    <AdminLayout>
        <div class="items-start">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Add New Portfolio Project</h1>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Project Name -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <InputLabel for="project_name" value="Project Name *" />
                        <TextInput
                            id="project_name"
                            v-model="form.project_name"
                            type="text"
                            class="mt-1 block w-full"
                            required
                            autofocus
                            placeholder="Enter project name"
                        />
                        <InputError class="mt-2" :message="form.errors.project_name" />
                    </div>

                    <!-- URL -->
                    <div>
                        <InputLabel for="url" value="Project URL" />
                        <TextInput
                            id="url"
                            v-model="form.url"
                            type="url"
                            class="mt-1 block w-full"
                            placeholder="https://example.com"
                        />
                        <InputError class="mt-2" :message="form.errors.url" />
                    </div>

                    <!-- Description -->
                    <div>
                        <InputLabel for="description" value="Description" />
                        <textarea
                            id="description"
                            v-model="form.description"
                            rows="3"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            placeholder="Describe your project..."
                        ></textarea>
                        <InputError class="mt-2" :message="form.errors.description" />
                    </div>

                    <!-- Technology -->
                    <div>
                        <InputLabel for="technology" value="Technologies Used" />
                        <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4">
                            <div v-for="tech in technologies" :key="tech.id" class="flex items-center">
                                <input
                                    :id="`tech-${tech.id}`"
                                    v-model="form.technology"
                                    :value="tech.id"
                                    type="checkbox"
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                />
                                <label :for="`tech-${tech.id}`" class="ml-2 text-sm text-gray-700">
                                    {{ tech.name }}
                                </label>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Select all technologies used in this project</p>
                        <InputError class="mt-2" :message="form.errors.technology" />
                    </div>

                    <!-- Login Credentials -->
                    <div>
                        <InputLabel for="login_id" value="Login ID" />
                        <TextInput
                            id="login_id"
                            v-model="form.login_id"
                            type="text"
                            class="mt-1 block w-full"
                            placeholder="Enter login ID (optional)"
                        />
                        <InputError class="mt-2" :message="form.errors.login_id" />
                    </div>

                    <div>
                        <InputLabel for="password" value="Password" />
                        <TextInput
                            id="password"
                            v-model="form.password"
                            type="text"
                            class="mt-1 block w-full"
                            placeholder="Enter password (optional)"
                        />
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>
                    </div>
                    <!-- Submit Buttons -->
                    <div class="flex mt-6 items-center justify-between">
                        <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('portfolios.index')">
                                <template #svg>
                                    <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                                </template>
                        </SvgLink>
                        <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>
