<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, router } from '@inertiajs/vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps(['template']);

const deleteTemplate = () => {
    if (confirm('Are you sure you want to delete this template?')) {
        router.delete(route('templates.destroy', props.template.id));
    }
};
</script>

<template>
    <Head :title="`Template: ${template.subject}`" />

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Template Details</h1>
                </div>
                <div>
                    <Link :href="route('templates.index')" class="text-sm text-gray-600 hover:text-gray-900">
                        ← Back to Templates
                    </Link>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="space-y-6">
                            <!-- Subject -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Subject</h3>
                                <p class="text-gray-700 bg-gray-50 p-3 rounded-md">{{ template.subject }}</p>
                            </div>

                            <!-- Content -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Content</h3>
                                <div class="bg-gray-50 p-4 rounded-md">
                                    <div class="prose prose-sm max-w-none" v-html="template.content"></div>
                                </div>
                            </div>

                            <!-- Raw Content (for editing reference) -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Raw Content</h3>
                                <pre class="bg-gray-100 p-4 rounded-md text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">{{ template.content }}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1 space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <PrimaryButton class="w-full" @click="$inertia.visit(route('templates.edit', template.id))">
                                Edit Template
                            </PrimaryButton>
                            <SecondaryButton class="w-full text-red-600 border-red-300 hover:bg-red-50" @click="deleteTemplate">
                                Delete Template
                            </SecondaryButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
