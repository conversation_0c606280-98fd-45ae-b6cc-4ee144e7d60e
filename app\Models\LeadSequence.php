<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeadSequence extends Model
{
    use HasFactory;

    use HasFactory;

    use SoftDeletes;

    protected $table = 'lead_sequences';

    protected $fillable = [
        'lead_id',
        'sequence_id',
        'current_step',
        'next_scheduled_at',
        'sent_time',
        'subject',
        'content',
        'full_content',
        'status',
        'opened_at',
        'clicked_at'
    ];

    public function lead()
    {
        return $this->belongsTo(Leads::class);
    }

    public function sequence()
    {
        return $this->belongsTo(Sequences::class);
    }
}
