<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProspectUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'nullable',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'lead_source' => 'nullable|in:linkedin,upwork_jignesh, upwork_abhishek,email,referral,website,cold_call,social_media,other',
            'lead_source_details' => 'nullable|string|max:500',
            'linkedin_url' => 'nullable|url',
            'website_url' => 'nullable|url',
            'company_website' => 'nullable|url',
            'status' => 'nullable|in:new,contacted,qualified,unqualified,converted,lost',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'score' => 'nullable|integer|min:0|max:100',
            'initial_conversation' => 'nullable|string',
            'notes' => 'nullable|string',
            'next_follow_up_at' => 'nullable|date',
            'estimated_budget' => 'nullable|numeric|min:0',
            'project_type' => 'nullable|string|max:255',
            'requirements' => 'nullable|string',
            'budget_range' => 'nullable|in:under_1k,1k_5k,5k_10k,10k_25k,25k_50k,over_50k',
            'assigned_to' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered as a prospect.',
            'lead_source.required' => 'Lead source is required.',
            'lead_source.in' => 'Please select a valid lead source.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Please select a valid priority.',
            'score.integer' => 'Score must be a number.',
            'score.min' => 'Score must be at least 0.',
            'score.max' => 'Score cannot be more than 100.',
            'linkedin_url.url' => 'LinkedIn URL must be a valid URL.',
            'website_url.url' => 'Website URL must be a valid URL.',
            'company_website.url' => 'Company website must be a valid URL.',
            'estimated_budget.numeric' => 'Estimated budget must be a number.',
            'estimated_budget.min' => 'Estimated budget cannot be negative.',
            'assigned_to.exists' => 'Selected user does not exist.',
        ];
    }
}
