import{r as p,x as y,o as s,c as o,a as i,u as h,w as _,F as x,Z as w,b as e,k as b,y as k,g as u,d as j,f as d,O as f,t as r,j as g,p as I,m as V}from"./app-b1cf8b3a.js";import{_ as C}from"./AdminLayout-13d09996.js";import{_ as M}from"./Pagination-4e2dfbea.js";import{_ as N}from"./CreateButton-9e45a5d4.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";const a=l=>(I("data-v-fe32a79d"),l=l(),V(),l),S={class:"animate-top"},B={class:"flex justify-between items-center"},D=a(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Portfolio")],-1)),$={class:"flex justify-end"},A={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},T={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},z=a(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),E={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},F={class:"flex justify-end"},H={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8"},L={class:"p-6"},O={class:"flex justify-between items-start mb-4"},G={class:"text-lg font-semibold text-gray-900 truncate"},U={class:"flex space-x-2"},Z=["onClick"],q={key:0,class:"mb-2"},J=["href"],K={key:1,class:"text-gray-500 text-sm mb-2 line-clamp-3"},Q={key:2,class:"mb-2 flex items-start"},R=a(()=>e("p",{class:"text-sm text-gray-500"},"Technologies:",-1)),W={class:"text-xs text-gray-700 ml-2"},X={key:3},Y={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-2 space-y-1"},ee={key:0,class:"flex justify-between items-center"},te=a(()=>e("span",{class:"text-xs font-medium text-gray-700"},"Login ID:",-1)),se={class:"text-xs text-gray-900 font-mono"},oe={key:1,class:"flex justify-between items-center"},ae=a(()=>e("span",{class:"text-xs font-medium text-gray-700"},"Password:",-1)),le={class:"text-xs text-gray-900 font-mono"},de={class:"flex justify-between items-center mt-2"},ie=a(()=>e("div",{class:"text-xs text-gray-500"},null,-1)),re={class:"flex space-x-2"},ne={key:1,class:"bg-white rounded-lg shadow p-12 text-center mt-8"},ce=a(()=>e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)),_e=a(()=>e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No portfolio items",-1)),he=a(()=>e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating your first portfolio project.",-1)),ue=a(()=>e("div",{class:"mt-6"},null,-1)),me=[ce,_e,he,ue],xe={key:2,class:"mt-6"},fe={__name:"Index",props:["portfolios","filters"],setup(l){const c=p(l.filters.search||"");y([c],()=>{f.get(route("portfolios.index"),{search:c.value},{preserveState:!0,replace:!0})},{debounce:300});const v=n=>{confirm("Are you sure you want to delete this portfolio item?")&&f.delete(route("portfolios.destroy",n))};return(n,m)=>(s(),o(x,null,[i(h(w),{title:"Portfolio"}),i(C,null,{default:_(()=>[e("div",S,[e("div",B,[D,e("div",$,[e("div",A,[e("div",T,[z,b(e("input",{id:"search-field","onUpdate:modelValue":m[0]||(m[0]=t=>c.value=t),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[k,c.value]])])]),e("div",E,[e("div",F,[i(N,{href:n.route("portfolios.create")},{default:_(()=>[u(" Add New Project ")]),_:1},8,["href"])])])])]),l.portfolios.data.length>0?(s(),o("div",H,[(s(!0),o(x,null,j(l.portfolios.data,t=>(s(),o("div",{key:t.id,class:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"},[e("div",L,[e("div",O,[e("h3",G,r(t.project_name),1),e("div",U,[i(h(g),{href:n.route("portfolios.edit",t.id),class:"text-blue-600 font-semibold hover:text-blue-800 text-sm"},{default:_(()=>[u(" Edit ")]),_:2},1032,["href"]),e("button",{onClick:ve=>v(t.id),class:"text-red-600 font-semibold hover:text-red-800 text-sm"}," Delete ",8,Z)])]),t.url?(s(),o("div",q,[e("a",{href:t.url,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm break-all"},r(t.url),9,J)])):d("",!0),t.description?(s(),o("p",K,r(t.description),1)):d("",!0),t.technology?(s(),o("div",Q,[R,e("p",W,r(t.technology.join(", ")),1)])):d("",!0),t.login_id||t.password?(s(),o("div",X,[e("div",Y,[t.login_id?(s(),o("div",ee,[te,e("span",se,r(t.login_id),1)])):d("",!0),t.password?(s(),o("div",oe,[ae,e("span",le,r(t.password),1)])):d("",!0)])])):d("",!0),e("div",de,[ie,e("div",re,[i(h(g),{href:n.route("portfolios.show",t.id),class:"text-blue-600 font-semibold hover:text-blue-800 text-sm"},{default:_(()=>[u(" View Project → Details ")]),_:2},1032,["href"])])])])]))),128))])):(s(),o("div",ne,me)),l.portfolios.data.length>0?(s(),o("div",xe,[i(M,{links:l.portfolios.links},null,8,["links"])])):d("",!0)])]),_:1})],64))}},je=P(fe,[["__scopeId","data-v-fe32a79d"]]);export{je as default};
