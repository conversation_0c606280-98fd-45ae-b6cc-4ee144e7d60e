<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

const props = defineProps({
  data: Object,
  sequences: Array,
  filters: Object,
});

// Form and modal state
const form = useForm({});
const modalVisible = ref(false);
const selectedEmailId = ref(null);
const rescheduleModalVisible = ref(false);
const editModalVisible = ref(false);
const editForm = useForm({
  id: null,
  email: '',
  reschedule: true
});

// Filter state
const sequenceId = ref(props.filters?.sequence_id || null);
const searchValue = ref(props.filters?.search || '');

// Email content and preview state
const showContentModal = ref(false);
const selectedContent = ref('');
const showErrorModal = ref(false);
const selectedError = ref('');
const selectedEmail = ref(null);

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return date.toLocaleDateString('en-US', options);
};

const formatTimeDate = (dateString) => {
  if (!dateString) return { time: '-', date: '-' };

  // Create UTC date manually to prevent local interpretation
  const utcDateParts = dateString.split(/[- :]/);
  const utcDate = new Date(Date.UTC(
    parseInt(utcDateParts[0]),      // Year
    parseInt(utcDateParts[1]) - 1,  // Month (0-based)
    parseInt(utcDateParts[2]),      // Day
    parseInt(utcDateParts[3]),      // Hour
    parseInt(utcDateParts[4]),      // Minute
    parseInt(utcDateParts[5])       // Second
  ));

  // Convert to IST
  const time = utcDate.toLocaleTimeString('en-IN', {
    hour: '2-digit', minute: '2-digit', hour12: true, timeZone: 'Asia/Kolkata',
  });

  const formattedDate = utcDate.toLocaleDateString('en-IN', {
    year: 'numeric', month: 'short', day: 'numeric', timeZone: 'Asia/Kolkata',
  });

  return {time, date: formattedDate};
};

const openDeleteModal = (id) => {
  selectedEmailId.value = id;
  modalVisible.value = true;
};

const closeModal = () => {
  modalVisible.value = false;
};

const openRescheduleModal = (id) => {
  selectedEmailId.value = id;
  rescheduleModalVisible.value = true;
};

const closeRescheduleModal = () => {
  rescheduleModalVisible.value = false;
};

const openContentModal = (content) => {
  selectedContent.value = content;
  showContentModal.value = true;
};

const closeContentModal = () => {
  showContentModal.value = false;
};

const openErrorModal = (error) => {
  selectedError.value = error;
  showErrorModal.value = true;
};

const closeErrorModal = () => {
  showErrorModal.value = false;
};

const setSequence = (id) => {
  sequenceId.value = id;
  handleSearchChange();
};

const handleSearchChange = (value) => {
  if (value !== undefined) {
    searchValue.value = value;
  }

  form.get(route('failed-emails.index', {
    search: searchValue.value,
    sequence_id: sequenceId.value,
  }), {
    preserveState: true,
  });
};

const deleteFailedEmail = () => {
  form.delete(route('sent-email.destroy', { id: selectedEmailId.value }), {
    onSuccess: () => closeModal()
  });
};

const rescheduleEmail = () => {
  form.post(route('failed-emails.reschedule', { id: selectedEmailId.value }), {
    onSuccess: (response) => {
      closeRescheduleModal();
      // Show success message
      const message = response?.data?.message || 'Email rescheduled successfully';
      alert(message);
    }
  });
};

const openEditModal = (email) => {
  selectedEmail.value = email;
  editForm.id = email.id;
  editForm.email = email.lead.email;
  editForm.reschedule = true;
  editModalVisible.value = true;
};

const closeEditModal = () => {
  editModalVisible.value = false;
  editForm.reset();
  selectedEmail.value = null;
};

const updateEmail = () => {
  editForm.patch(route('failed-emails.update'), {
    onSuccess: (response) => {
      closeEditModal();
      // Show success message
      const message = response?.data?.message || 'Email updated successfully';
      alert(message);
    }
  });
};
</script>

<template>
  <Head title="Failed Emails" />

  <AdminLayout>
    <div class="animate-top">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-semibold leading-7 text-gray-900">Failed Emails</h1>
        </div>
        <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
          <InputLabel for="search_field" />
          <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20"
              fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd"
                d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                clip-rule="evenodd"></path>
            </svg>
            <input type="text" name="search" id="search_field" v-model="searchValue" @input="handleSearchChange($event.target.value)"
              class="block w-full rounded-lg border-0 py-1.5 pl-8 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              placeholder="Search">
          </div>
        </div>
      </div>

      <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
        <div class="flex mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
          </svg>
          <InputLabel for="customer_id" value="Filters" />
        </div>
        <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
          <div class="sm:col-span-4">
            <InputLabel for="sequence_id" value="Sequence" />
            <div class="relative mt-2">
              <SearchableDropdownNew
                :options="[{ id: '', name: 'All Sequence' }, ...sequences]"
                v-model="sequenceId"
                @onchange="(id) => setSequence(id)"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="mt-8 overflow-x-auto sm:rounded-lg">
        <div class="shadow sm:rounded-lg">
          <table class="w-full text-sm text-left rtl:text-right text-gray-500">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
              <tr class="border-b-2">
                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Recipient</th>
                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Sequence</th>
                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Subject</th>
                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">ACTION</th>
              </tr>
            </thead>
            <tbody v-if="data.data && (data.data.length > 0)">
              <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="email in data.data" :key="email.id">
                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">
                  <div>{{ email.lead.first_name ?? '-' }} {{ email.lead.last_name ?? '-' }}</div>
                  <div class="text-xs text-red-600">{{ email.lead.email ?? '-' }}</div>
                </td>
                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">{{ email.sequence.name ?? '-' }}</td>
                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                  <button @click="openContentModal(email.content)">
                    {{ email.subject || '-' }}
                  </button>
                </td>
                <td class="items-center px-4 py-2.5">
                  <div class="flex items-center justify-start gap-4">
                    <Dropdown :align="'right'" width="48">
                      <template #trigger>
                        <button type="button" title="Open details" class="p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100">
                          <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                            <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                          </svg>
                        </button>
                      </template>
                      <template #content>
                        <button type="button" @click="openEditModal(email)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                          </svg>
                          <span class="text-sm text-gray-700 leading-5">
                            Edit
                          </span>
                        </button>
                        <button type="button" @click="openRescheduleModal(email.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                          </svg>
                          <span class="text-sm text-gray-700 leading-5">
                            Reschedule
                          </span>
                        </button>
                        <button type="button" @click="openDeleteModal(email.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                          </svg>
                          <span class="text-sm text-gray-700 leading-5">
                            Delete
                          </span>
                        </button>
                      </template>
                    </Dropdown>
                  </div>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr class="bg-white">
                <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                  No failed emails found.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>

      <!-- Delete Confirmation Modal -->
      <Modal :show="modalVisible" @close="closeModal">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900">
            Are you sure you want to delete this failed email?
          </h2>
          <div class="mt-6 flex justify-end">
            <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
            <DangerButton class="ml-3" @click="deleteFailedEmail">
              Delete
            </DangerButton>
          </div>
        </div>
      </Modal>

      <!-- Reschedule Confirmation Modal -->
      <Modal :show="rescheduleModalVisible" @close="closeRescheduleModal">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900">
            Reschedule Email
          </h2>
          <p class="mt-2 text-sm text-gray-600">
            This will reset the email status and attempt to send it again. Are you sure you want to continue?
          </p>
          <div class="mt-6 flex justify-end">
            <SecondaryButton @click="closeRescheduleModal"> Cancel </SecondaryButton>
            <PrimaryButton class="ml-3" @click="rescheduleEmail">
              Reschedule
            </PrimaryButton>
          </div>
        </div>
      </Modal>

      <!-- Email Content Modal -->
      <Modal :show="showContentModal" @close="closeContentModal" max-width="2xl">
        <div class="p-0 max-h-[80vh] flex flex-col relative">
          <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
            <h2 class="text-base font-medium text-gray-900">Email Content</h2>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
              stroke="currentColor"
              class="h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500"
              @click="closeContentModal">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
          <div class="px-4 pb-4 overflow-y-auto">
            <div class="prose prose-sm" v-html="selectedContent || '-'"></div>
          </div>
        </div>
      </Modal>

      <!-- Error Details Modal -->
      <Modal :show="showErrorModal" @close="closeErrorModal" max-width="2xl">
        <div class="p-0 max-h-[80vh] flex flex-col relative">
          <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
            <h2 class="text-base font-medium text-red-600">Error Details</h2>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
              stroke="currentColor"
              class="h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500"
              @click="closeErrorModal">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
          <div class="px-4 py-4 overflow-y-auto">
            <div class="bg-red-50 border border-red-200 rounded p-4 text-red-800">
              {{ selectedError }}
            </div>
          </div>
        </div>
      </Modal>

      <!-- Edit Email Modal -->
      <Modal :show="editModalVisible" @close="closeEditModal" max-width="md">
        <div class="p-0 max-h-[80vh] flex flex-col relative">
          <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
            <h2 class="text-base font-medium text-gray-900">Edit Failed Email</h2>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
              stroke="currentColor"
              class="h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500"
              @click="closeEditModal">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
          <div class="px-6 py-4 overflow-y-auto">
            <form @submit.prevent="updateEmail">
              <!-- Email Address -->
              <div class="mb-4">
                <InputLabel for="email" value="Email Address" class="mb-1" />
                <TextInput
                  id="email"
                  type="email"
                  class="mt-1 block w-full"
                  v-model="editForm.email"
                  required
                  autocomplete="email"
                />
                <InputError class="mt-2" :message="editForm.errors.email" />
                <p class="mt-2 text-xs text-gray-500">
                  Correct the email address to fix the delivery issue.
                </p>
              </div>

              <!-- Email Information -->
              <div v-if="selectedEmail" class="mb-4">
                <div class="bg-gray-50 rounded-md p-4 border border-gray-200">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p class="text-gray-500">Sequence:</p>
                      <p class="font-medium">{{ selectedEmail.sequence.name }}</p>
                    </div>
                    <div>
                      <p class="text-gray-500">Subject:</p>
                      <p class="font-medium">{{ selectedEmail.subject }}</p>
                    </div>
                    <div>
                      <p class="text-gray-500">Lead:</p>
                      <p class="font-medium">{{ selectedEmail.lead.first_name }} {{ selectedEmail.lead.last_name }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Reschedule Notice -->
              <div class="mb-4">
                <div class="bg-blue-50 border border-blue-200 rounded p-3 text-blue-800 text-sm">
                  <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <p class="font-medium">Automatic Rescheduling</p>
                      <p>This email will be automatically rescheduled for sending after updating.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex items-center justify-between">
                <div class="ml-auto flex items-center justify-end space-x-4 mt-6">
                    <SecondaryButton type="button" @click="closeEditModal">Cancel</SecondaryButton>
                    <PrimaryButton type="submit" :disabled="editForm.processing">Update</PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </Modal>
    </div>
  </AdminLayout>
</template>
