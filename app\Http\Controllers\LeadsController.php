<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Leads;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\LeadsStoreRequest;
use App\Models\Sequences;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Mail;
use App\Mail\BulkEmail;
use App\Models\LeadSequence;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Response;
use App\Exports\LeadssampleExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Validation\Rule;

class LeadsController extends Controller
{


    public function index(Request $request)
    {
        $search = $request->input('search');
        $country = $request->input('country');
        $city = $request->input('city');
        $sequenceId = $request->input('sequence');
        $assignmentStatus = $request->input('assignment_status', 'unassigned'); // Default to unassigned

        $query = Leads::with('leadSequences.sequence');

        // Fix search query
        if ($search) {
            $query->where(function ($subQuery) use ($search) {
                $subQuery->where('country', 'like', "%$search%")
                        ->orWhere('city', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%");
            });
        }

        $assignedLeadIds = LeadSequence::pluck('lead_id')->toArray();

        // Filter by assignment status
        if ($assignmentStatus === 'assigned') {
            $query->whereIn('id', $assignedLeadIds);
        } elseif ($assignmentStatus === 'unassigned') {
            $query->whereNotIn('id', $assignedLeadIds);
        }
        // If 'all' is selected, no filtering is needed

        if (! empty($assignedLeadIds)) {
            $query->orderByRaw(
                "CASE WHEN id IN (" . implode(',', $assignedLeadIds) . ") THEN 1 ELSE 0 END ASC"
            );
        }

        if ($sequenceId) {
            $query->whereHas('leadSequences', function ($q) use ($sequenceId) {
                $q->where('sequence_id', $sequenceId);
            });
        }

        if ($country) $query->where('country', $country);
        if ($city) $query->where('city', $city);

        $existingInSequence = LeadSequence::pluck('lead_id')->toArray();

        $countries = Leads::select('country')->distinct()->whereNotNull('country')->orderBy('country')->pluck('country');

        $cities = Leads::select('city')->distinct()->whereNotNull('city')->orderBy('city')->pluck('city');

        $sequences = Sequences::whereNotNull('name')->orderBy('name')->get(['id', 'name']);

        $allLeadIds = (clone $query)->pluck('id')->toArray();

        $query->orderBy('id', 'desc');
        $data = $query->paginate(20)->withQueryString();

        return Inertia::render('Leads/List', [
            'data' => $data,
            'countries' => $countries,
            'cities' => $cities,
            'sequences' => $sequences,
            'existingInSequence' => $existingInSequence,
            'allLeadIds' => $allLeadIds,
            'filters' => $request->only(['search', 'country', 'city', 'sequence', 'assignment_status']),
        ]);
    }


    public function create()
    {
        return Inertia::render('Leads/Add');
    }

    public function store(LeadsStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            // $userData = $request->DTO()->toArray();
            $userData = $request->validated();
            $lead = Leads::create($userData);
            $lead->save();
            DB::commit();
            return Redirect::to('/leads')->with('success', 'Leads added successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/leads')->with('error', 'Failed to add leads: ' . $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Leads::find($id);
        return Inertia::render('Leads/Edit', compact('data'));
    }

    public function update(Request $request)
    {
        // Validate the request
        $request->validate(['email' => ['required','string','email','max:255',Rule::unique('leads')->ignore($request->id)]]);

        DB::beginTransaction();
        try {
            $lead = Leads::findOrFail($request->id);

            // Check if email already exists (excluding current lead)
            $emailExists = Leads::where('email', $request->email)
                ->where('id', '!=', $request->id)
                ->exists();

            if ($emailExists) {
                DB::rollBack();
                return back()->withErrors(['email' => 'This email is already in use.'])->withInput();
            }

            $lead->update($request->all());
            DB::commit();
            return Redirect::to('/leads')->with('success','Lead Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/leads')->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            Leads::findOrFail($id)->delete();
            return Redirect::to('/leads')->with('success', 'Leads Deleted Successfully');
        } catch (\Exception $e) {
            return Redirect::to('/leads')->with('error', 'An error occurred while deleting the user.');
        }
    }

    public function uploadXls()
    {
        return Inertia::render('Leads/Upload');
    }

    public function show(){

    }

    public function importXls(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xls,xlsx|max:2048'
        ]);

        $file = $request->file('file');
        $spreadsheet = IOFactory::load($file->getPathname());
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        if (count($rows) <= 1) {
            return Redirect::to('/leads')->with('error', 'The uploaded file does not contain any leads.');
        }

        $headers = array_map('strtolower', $rows[0]);

        $headers = array_map(fn($header) => str_replace(' ', '_', $header), $headers);

        $dataRows = array_slice($rows, 1);

        $processedEmails = [];
        $duplicateEmails = [];

        foreach ($dataRows as $row) {
            if (count($row) < count($headers)) {
                continue;
            }

            $data = array_combine($headers, $row);
            $email = strtolower(trim($data['email'] ?? ''));

            if (empty($email)) {
                continue;
            }

            if (in_array($email, $processedEmails)) {
                $duplicateEmails[] = $email;
            } else {
                $processedEmails[] = $email;
            }
        }

        if (!empty($duplicateEmails)) {
            $duplicateEmailsList = implode(', ', array_unique($duplicateEmails));
            return Redirect::to('/leads')->with('error', "Duplicate emails found: $duplicateEmailsList");
        }

        foreach ($dataRows as $row) {
            if (count($row) < count($headers)) {
                continue;
            }

            $data = array_combine($headers, $row);

            $email = strtolower(trim($data['email'] ?? ''));

            if (empty($email)) {
                continue;
            }

            if (in_array($email, $processedEmails) || Leads::where('email', $email)->exists()) {
                $duplicateEmails[] = $email;
            } else {
                $processedEmails[] = $email;
            }

            $insertedCount = 0;

            if (!Leads::where('email', $email)->exists()) {
                Leads::create([
                    'email' => $email,
                    'first_name' => $data['first_name'] ?? '',
                    'last_name' => $data['last_name'] ?? '',
                    'country' => $data['country'] ?? '',
                    'city' => $data['city'] ?? '',
                    'designation' => $data['designation'] ?? '',
                    'organization_name' => $data['organization_name'] ?? '',
                    'organization_website_url' => $data['organization_website_url'] ?? '',
                    'linkedin_url' => $data['linkedin_url'] ?? '',
                    'organization_linkedin_url' => $data['organization_linkedin_url'] ?? '',
                ]);
                $insertedCount++;
            }
        }

        if ($insertedCount > 0) {
            return Redirect::to('/leads')->with('success', 'Leads Uploaded Successfully');
        } else {
            return Redirect::to('/leads')->with('error', 'All emails in the uploaded file already exist. No new leads were added.');
        }
            }


    public function sendBulkEmails()
    {

        $leads = [
            '<EMAIL>',
            '<EMAIL>'
        ];

        $subject = "Important Update from Our Team!";
        $content = "This is a test bulk email sent using Laravel.";

        foreach ($leads as $email) {
            Mail::to($email)->send(new BulkEmail($subject, $content));
        }

        return response()->json(['message' => 'Bulk emails sent successfully!']);
    }

    public function downloadSampleXls()
    {
        return Excel::download(new LeadssampleExport, 'sample-leads.xlsx');
    }


}
