<?php
namespace App\ProcessTask\CompanyPO;

use App\Services\CompanyService;
use Closure;
use Illuminate\Http\Request;

class SavePurchaseOrder
{
    public $companyService;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    public function __invoke(Request $request, Closure $next)
    {
        //$this->companyService->saveCompanyPO($request);

        return $next($request);
    }
}
