<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { Head, useForm, usePage } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

const props = defineProps({
  data: Object,
  sequences: Array,
  status: Object,
  filters: Object,
});

// Form and modal state
const form = useForm({});
const modalVisible = ref(false);
const selectedUserId = ref(null);

// Filter state
const sequenceId = ref(props.filters?.sequence_id || null);
const statusId = ref(props.filters?.status || null);
const searchValue = ref(props.filters?.search || '');
const scheduledDate = ref(props.filters?.scheduled_date || null);

// Email content and preview state
const showContentModal = ref(false);
const selectedContent = ref('');
const showPreviewModal = ref(false);
const previewData = ref(null);
const isLoadingPreview = ref(false);
const showOriginalTemplate = ref(false);

const dateFilters = [
    { id: null, name: 'All Dates' },
    { id: 'today', name: 'Today' },
];

const getStatusBgClass = (status) => {
    switch (status) {
        case 'pending':
            return 'bg-blue-100';
        case 'In-process':
            return 'bg-yellow-100';
        case 'completed':
            return 'bg-green-100';

        default:
            return 'bg-gray-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'pending':
            return 'text-blue-600';
        case 'In-process':
            return 'text-yellow-600';
        case 'completed':
            return 'text-green-600';

        default:
            return 'text-gray-600';
    }
};

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const openContentModal = (content) => {
  selectedContent.value = content;
  showContentModal.value = true;
};

const closeContentModal = () => {
    showContentModal.value = false;
};

const closePreviewModal = () => {
    showPreviewModal.value = false;
    previewData.value = null;
    showOriginalTemplate.value = false;
};

const previewEmail = async (id) => {
    isLoadingPreview.value = true;
    showPreviewModal.value = true;

    try {
        const response = await fetch(route('email.preview', id));
        const data = await response.json();
        previewData.value = data;
    } catch (error) {
        console.error('Error fetching email preview:', error);
        previewData.value = {
            success: false,
            message: 'Failed to load preview. Please try again.'
        };
    } finally {
        isLoadingPreview.value = false;
    }
};

const setSequence = (id) => {
    sequenceId.value = id;
    handleSearchChange();
};

const setStatus = (id) => {
    statusId.value = id;
    handleSearchChange();
};

const setDateFilter = (id) => {
    scheduledDate.value = id;
    handleSearchChange();
};

const handleSearchChange = (value) => {
    if (value !== undefined) {
        searchValue.value = value;
    }

    form.get(route('sent-email.index', {
        search: searchValue.value,
        sequence_id: sequenceId.value,
        status: statusId.value,
        scheduled_date: scheduledDate.value,
    }), {
        preserveState: true,
    });
};

const deleteLeadSequence = () => {
    form.delete(route('sent-email.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatTimeDate = (dateString) => {
    if (!dateString) return { time: '-', date: '-' };

    // Create UTC date manually to prevent local interpretation
    const utcDateParts = dateString.split(/[- :]/);
    const utcDate = new Date(Date.UTC(
        parseInt(utcDateParts[0]),      // Year
        parseInt(utcDateParts[1]) - 1,  // Month (0-based)
        parseInt(utcDateParts[2]),      // Day
        parseInt(utcDateParts[3]),      // Hour
        parseInt(utcDateParts[4]),      // Minute
        parseInt(utcDateParts[5])       // Second
    ));

    // Convert to IST
    const time = utcDate.toLocaleTimeString('en-IN', {
        hour: '2-digit', minute: '2-digit', hour12: true, timeZone: 'Asia/Kolkata',
    });

    const formattedDate = utcDate.toLocaleDateString('en-IN', {
        year: 'numeric', month: 'short', day: 'numeric', timeZone: 'Asia/Kolkata',
    });

    return {time,date: formattedDate};
};

// Form and modal state

const selectedEmail = ref(null);
const selectedEmailId = ref(null);
const editModalVisible = ref(false);
const editForm = useForm({
  id: null,
  email: '',
  reschedule: true
});

const openEditModal = (email) => {

  selectedEmail.value = email;
  editForm.id = email.id;
  editForm.email = email.lead.email;
  editForm.reschedule = true;
  editModalVisible.value = true;
};

const closeEditModal = () => {
  editModalVisible.value = false;
  editForm.reset();
  selectedEmail.value = null;
};

const updateEmail = () => {
  editForm.patch(route('failed-emails.update'), {
    onSuccess: (response) => {
      closeEditModal();
    }
  });
};

</script>

<template>
    <Head title="Sent Emails"/>

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Sent Emails</h1>
            </div>
            <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                <InputLabel for="search_field"/>
                <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                    <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                    </svg>
                    <input id="search-field" @input="handleSearchChange($event.target.value)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                </div>
            </div>
            <!-- <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <CreateButton v-if="hasScheduledToday" @click="sendScheduledEmails" :disabled="isSending">
                    <template #default>
                        <div class="flex items-center space-x-2">
                            <svg v-if="isSending" class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                            </svg>
                            <span>{{ isSending ? 'Sending...' : 'Send Scheduled E-mails' }}</span>
                        </div>
                    </template>
                </CreateButton>
            </div> -->
        </div>
        <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                </svg>
                <InputLabel for="customer_id" value="Filters" />
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-4">
                    <InputLabel for="sequence_id" value="Sequence" />
                    <div class="relative mt-2">
                        <SearchableDropdownNew
                        :options="[{ id: '', name: 'All Sequence' }, ...sequences]"
                        v-model="sequenceId"
                        @onchange="(id) => setSequence(id)"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="status_id" value="Status" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="status"
                            v-model="statusId"
                            @onchange="setStatus"
                            />
                    </div>
                </div>
                <div class="sm:col-span-4">
                    <InputLabel for="scheduled_date" value="Date" />
                    <div class="relative mt-2">
                        <SimpleDropdown
                            :options="dateFilters"
                            v-model="scheduledDate"
                            @onchange="setDateFilter"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Recipient</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Sequence</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Step</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Subject</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Next Schedule</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Sent At</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">Status</th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer">ACTION</th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="userData in data.data" :key="userData.id">
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">
                                <div>{{ userData.lead.first_name ?? '-' }} {{ userData.lead.last_name ?? '-' }}</div>
                                <div class="text-xs text-blue-600">{{ userData.lead.email ?? '-' }}</div>
                            </td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">{{ userData.sequence.name ?? '-' }}</td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">{{ userData.current_step ?? '-' }}</td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                <button
                                  @click="previewEmail(userData.id)"
                                >
                                  {{ userData.subject || '-' }}
                                </button>
                            </td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">{{ formatDate(userData.next_scheduled_at) ?? '-' }}</td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900">
                                <div v-if="userData.sent_time">
                                    <div>{{ formatTimeDate(userData.sent_time).time }}</div>
                                    <div class="text-xs text-gray-500">{{ formatTimeDate(userData.sent_time).date }}</div>
                                </div>
                                <div v-else>-</div>
                            </td>
                            <td class="py-2.5">
                                <div class="flex justify-center items-center w-full">
                                    <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(userData.status)">
                                        <span class="text-sm font-semibold" :class="getStatusClass(userData.status)">{{ userData.status }}</span>
                                    </div>
                                </div>
                            </td>

                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                     <Dropdown :align="'right'" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink v-if="userData.status == 'pending'" :href="route('sent-email.edit',{id:userData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button  v-if="userData.status == 'pending'" type="button" @click="openDeleteModal(userData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <button v-if="userData.status == 'completed'" type="button" @click="openEditModal(userData)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Reschedule
                                                </span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No emails found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
         <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteLeadSequence"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="showContentModal" @close="closeContentModal" max-width="2xl">
            <div class="p-0 max-h-[80vh] flex flex-col relative">
                <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
                    <h2 class="text-base font-medium text-gray-900">Email Content</h2>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor"
                        class="h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500"
                        @click="closeContentModal">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </div>
                <div class="px-4 pb-4 overflow-y-auto">
                    <div class="prose prose-sm" v-html="selectedContent || '-'"></div>
                </div>
            </div>
        </Modal>

        <!-- Email Preview Modal -->
        <Modal :show="showPreviewModal" @close="closePreviewModal" max-width="2xl">
            <div class="p-0 max-h-[80vh] flex flex-col relative">
                <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <h2 class="text-base font-medium text-gray-900">Email Preview</h2>
                            <div v-if="previewData && previewData.isSent" class="ml-3 bg-green-100 text-green-800 px-3 py-0.5 rounded-full text-xs font-medium">
                                Completed
                            </div>
                            <div v-if="previewData && previewData.isSent" class="flex flex-col items-end">
                                <div class="text-gray-500 text-xs ml-2">
                                    At {{ formatTimeDate(previewData.sentTime).time }} on {{ formatTimeDate(previewData.sentTime).date }}.
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor"
                                class="h-5 w-5 cursor-pointer text-gray-500 hover:text-red-500"
                                @click="closePreviewModal">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div v-if="isLoadingPreview" class="flex justify-center items-center p-6">
                    <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3" opacity="0.2"/>
                        <path stroke="currentColor" stroke-linecap="round" stroke-width="3" d="M22 12a10 10 0 0 0-20 0" opacity="0.8"/>
                    </svg>
                </div>
                <div v-else-if="previewData && previewData.success" class="px-4 pb-4 overflow-y-auto">
                    <div class="bg-gray-100 p-3 mb-3 rounded text-sm mt-2">
                        <div class="flex justify-between items-start">
                            <div class="space-y-2">
                                <div>
                                    <span class="text-gray-600 font-medium">From:</span>
                                    <span>{{ previewData.from.name }} &lt;{{ previewData.from.email }}&gt;</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 font-medium">To:</span>
                                    <span>{{ previewData.recipient }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 font-medium">Subject:</span>
                                    <span>{{ previewData.subject }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Completed Email Notice -->
                    <div v-if="previewData.isSent" class="bg-green-50 border border-green-200 text-green-800 p-3 mb-3 rounded text-xs">
                        <div class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="font-medium">Viewing Completed Email</p>
                                <p>This preview shows the exact content that was sent to the recipient, including the company information as it appeared at the time of sending. Any changes made to templates or company information after completion are not reflected here.</p>
                            </div>
                            <div v-if="showOriginalTemplate && !previewData.isSent" class="border p-3 rounded bg-white">
                        <div class="prose prose-sm max-w-none" v-html="previewData.originalContent"></div>
                    </div>
                        </div>
                    </div>
                    <div v-if="showOriginalTemplate && !previewData.isSent" class="border p-3 rounded bg-white">
                        <div class="prose prose-sm max-w-none" v-html="previewData.originalContent"></div>
                    </div>

                    <!-- Processed View -->
                    <div v-else class="border p-3 rounded bg-white">
                        <div v-if="!previewData.isSent" class="bg-green-50 border border-green-200 text-green-800 p-2 mb-3 rounded text-xs">
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <p class="font-medium">Processed View</p>
                                    <p>This shows how the email will look when sent.</p>
                                </div>
                            </div>
                        </div>
                        <div class="prose prose-sm max-w-none" v-html="previewData.content"></div>
                    </div>
                    <!-- Lead Data Reference -->
                    <div v-if="previewData.lead" class="mt-3 border border-gray-200 rounded p-3 bg-gray-50">
                        <h3 class="text-xs font-medium text-gray-700 mb-2">Lead Data Used in Email:</h3>
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div><span class="font-medium">Name:</span> {{ previewData.lead.first_name }} {{ previewData.lead.last_name }}</div>
                            <div><span class="font-medium">Email:</span> {{ previewData.lead.email }}</div>
                            <div><span class="font-medium">Organization:</span> {{ previewData.lead.organization }}</div>
                        </div>
                    </div>
                </div>
                <div v-else-if="previewData && !previewData.success" class="px-4 py-6 text-center">
                    <div class="text-red-500 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mx-auto">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    <p class="text-gray-700 text-sm">{{ previewData.message }}</p>
                </div>
            </div>
        </Modal>
        <!-- Edit Email Modal -->
        <Modal :show="editModalVisible" @close="closeEditModal" max-width="md">
        <div class="p-0 max-h-[80vh] flex flex-col relative">
        <div class="sticky top-0 bg-white z-10 px-4 py-3 border-b">
            <h2 class="text-base font-medium text-gray-900">Edit Failed Email</h2>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor"
            class="h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500"
            @click="closeEditModal">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </div>
        <div class="px-6 py-4 overflow-y-auto">
            <form @submit.prevent="updateEmail">
            <!-- Email Address -->
            <div class="mb-4">
                <InputLabel for="email" value="Email Address" class="mb-1" />
                <TextInput
                id="email"
                type="email"
                class="mt-1 block w-full"
                v-model="editForm.email"
                required
                autocomplete="email"
                />
                <InputError class="mt-2" :message="editForm.errors.email" />
                <p class="mt-2 text-xs text-gray-500">
                Correct the email address to fix the delivery issue.
                </p>
            </div>

            <!-- Email Information -->
            <div v-if="selectedEmail" class="mb-4">
                <div class="bg-gray-50 rounded-md p-4 border border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                    <p class="text-gray-500">Sequence:</p>
                    <p class="font-medium">{{ selectedEmail.sequence.name }}</p>
                    </div>
                    <div>
                    <p class="text-gray-500">Subject:</p>
                    <p class="font-medium">{{ selectedEmail.subject }}</p>
                    </div>
                    <div>
                    <p class="text-gray-500">Lead:</p>
                    <p class="font-medium">{{ selectedEmail.lead.first_name }} {{ selectedEmail.lead.last_name }}</p>
                    </div>
                </div>
                </div>
            </div>

            <!-- Reschedule Notice -->
            <div class="mb-4">
                <div class="bg-blue-50 border border-blue-200 rounded p-3 text-blue-800 text-sm">
                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                    <p class="font-medium">Automatic Rescheduling</p>
                    <p>This email will be automatically rescheduled for sending after updating.</p>
                    </div>
                </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between">
                <div class="ml-auto flex items-center justify-end space-x-4 mt-6">
                    <SecondaryButton type="button" @click="closeEditModal">Cancel</SecondaryButton>
                    <PrimaryButton type="submit" :disabled="editForm.processing">Update</PrimaryButton>
                </div>
            </div>
            </form>
        </div>
        </div>
        </Modal>
    </AdminLayout>

</template>
