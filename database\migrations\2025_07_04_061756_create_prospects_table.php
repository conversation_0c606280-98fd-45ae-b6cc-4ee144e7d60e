<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prospects', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('position')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();

            // Lead source tracking
            $table->enum('lead_source', ['linkedin', 'upwork', 'email', 'referral', 'website', 'cold_call', 'social_media', 'other'])->default('other');
            $table->string('lead_source_details')->nullable(); // Additional details about the source

            // URLs and social profiles
            $table->string('linkedin_url')->nullable();
            $table->string('website_url')->nullable();
            $table->string('company_website')->nullable();

            // Prospect status and qualification
            $table->enum('status', ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'])->default('new');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->integer('score')->default(0); // Lead scoring 0-100

            // Conversation and interaction tracking
            $table->text('initial_conversation')->nullable();
            $table->text('notes')->nullable();
            $table->json('conversation_history')->nullable(); // Store conversation timeline
            $table->timestamp('last_contacted_at')->nullable();
            $table->timestamp('next_follow_up_at')->nullable();

            // Budget and project information
            $table->decimal('estimated_budget', 10, 2)->nullable();
            $table->string('project_type')->nullable();
            $table->text('requirements')->nullable();
            $table->enum('budget_range', ['under_1k', '1k_5k', '5k_10k', '10k_25k', '25k_50k', 'over_50k'])->nullable();

            // Conversion tracking
            $table->foreignId('converted_lead_id')->nullable()->constrained('leads')->onDelete('set null');
            $table->timestamp('converted_at')->nullable();

            // Assignment and ownership
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');

            $table->softDeletes();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['status', 'priority']);
            $table->index(['lead_source']);
            $table->index(['assigned_to']);
            $table->index(['next_follow_up_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prospects');
    }
};
