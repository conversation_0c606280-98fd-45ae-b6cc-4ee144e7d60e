<?php

namespace App\Observers;

use App\Models\Product;

class ProductObserver
{
    public function created(Product $product)
    {
        ProductLog::create([
            'product_id' => $product->id,
            'type' => 'created',
            'quantity_change' => $product->quantity,
        ]);
    }

    public function updated(Product $product)
    {
        $originalQuantity = $product->getOriginal('quantity');
        $newQuantity = $product->quantity;
        $quantityChange = $newQuantity - $originalQuantity;

        ProductLog::create([
            'product_id' => $product->id,
            'type' => 'updated',
            'quantity_change' => $quantityChange,
        ]);
    }

    public function deleted(Product $product)
    {
        ProductLog::create([
            'product_id' => $product->id,
            'type' => 'deleted',
        ]);
    }

    public function restored(Product $product): void
    {
        //
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Product $product): void
    {
        //
    }
}
