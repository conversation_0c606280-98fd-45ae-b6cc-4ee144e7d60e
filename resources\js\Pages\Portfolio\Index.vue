<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import Pagination from '@/Components/Pagination.vue';
import CreateButton from '@/Components/CreateButton.vue';

const props = defineProps(['portfolios', 'filters']);

const search = ref(props.filters.search || '');

// Watch for changes and update URL
watch([search], () => {
    router.get(route('portfolios.index'), {
        search: search.value,
    }, {
        preserveState: true,
        replace: true,
    });
}, { debounce: 300 });


const deletePortfolio = (id) => {
    if (confirm('Are you sure you want to delete this portfolio item?')) {
        router.delete(route('portfolios.destroy', id));
    }
};
</script>

<template>
    <Head title="Portfolio" />

    <AdminLayout>
        <div class="animate-top">

            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Portfolio</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                            </svg>
                            <input id="search-field"  v-model="search" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                        </div>
                    </div>
                    <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                        <div class="flex justify-end">
                            <CreateButton :href="route('portfolios.create')">
                                    Add New Project
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Portfolio Grid -->
            <div v-if="portfolios.data.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                <div v-for="portfolio in portfolios.data" :key="portfolio.id"
                     class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 truncate">
                                {{ portfolio.project_name }}
                            </h3>
                            <div class="flex space-x-2">
                                <Link :href="route('portfolios.edit', portfolio.id)"
                                      class="text-blue-600 font-semibold hover:text-blue-800 text-sm">
                                    Edit
                                </Link>
                                <button @click="deletePortfolio(portfolio.id)"
                                        class="text-red-600 font-semibold hover:text-red-800 text-sm">
                                    Delete
                                </button>
                            </div>
                        </div>
                        <div v-if="portfolio.url" class="mb-2">
                            <a :href="portfolio.url"
                                target="_blank"
                                class="text-blue-600 hover:text-blue-800 text-sm break-all">
                                {{ portfolio.url }}
                            </a>
                        </div>
                        <p v-if="portfolio.description" class="text-gray-500 text-sm mb-2 line-clamp-3">
                            {{ portfolio.description }}
                        </p>
                        <div v-if="portfolio.technology" class="mb-2 flex items-start">
                            <p class="text-sm text-gray-500">Technologies:</p>
                            <p class="text-xs text-gray-700 ml-2">{{ portfolio.technology.join(', ') }}</p>
                        </div>
                        <!-- Login Credentials -->
                        <div v-if="portfolio.login_id || portfolio.password">
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2 space-y-1">
                                <div v-if="portfolio.login_id" class="flex justify-between items-center">
                                    <span class="text-xs font-medium text-gray-700">Login ID:</span>
                                    <span class="text-xs text-gray-900 font-mono">{{ portfolio.login_id }}</span>
                                </div>
                                <div v-if="portfolio.password" class="flex justify-between items-center">
                                    <span class="text-xs font-medium text-gray-700">Password:</span>
                                    <span class="text-xs text-gray-900 font-mono">{{ portfolio.password }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-2">
                            <div class="text-xs text-gray-500">
                            </div>
                            <div class="flex space-x-2">
                                <Link :href="route('portfolios.show', portfolio.id)"
                                      class="text-blue-600 font-semibold hover:text-blue-800 text-sm">
                                   View Project → Details
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else class="bg-white rounded-lg shadow p-12 text-center mt-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No portfolio items</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first portfolio project.</p>
                <div class="mt-6">
                </div>
            </div>

            <!-- Pagination -->
            <div v-if="portfolios.data.length > 0" class="mt-6">
                <Pagination :links="portfolios.links" />
            </div>
        </div>
    </AdminLayout>
</template>

<style scoped>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
