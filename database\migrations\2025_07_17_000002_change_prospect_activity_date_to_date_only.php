<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('prospect_activities', function (Blueprint $table) {
            // Change timestamp field to date field
            $table->date('activity_date')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('prospect_activities', function (Blueprint $table) {
            // Revert back to timestamp field
            $table->timestamp('activity_date')->change();
        });
    }
};
