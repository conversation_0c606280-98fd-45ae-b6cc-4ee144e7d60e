<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maat<PERSON>bsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OrdersExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;

    public function __construct(array $allData)
    {
        $this->allData = $allData;
    }

    public function title(): string
    {
        return 'Orders Report as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        return [
            "Order Number",
            "Quotation Number",
            "Customer Name",
            "Sales Person",
            "Date",
            "Total Amount",
            "Status"
        ];
    }

    public function collection()
    {
        $data = $this->allData;
        $columns = [];

        if (count($data) > 0) {
            foreach ($data as $order) {
                // Collect the order data into an array
                $columns[] = [
                    $order['order_number'],
                    $order['quotation']['quotation_number'] ?? null,
                    $order['customers']['customer_name'] ?? null,
                    ($order['users']['first_name'] ?? '') . ' ' . ($order['users']['last_name'] ?? ''),
                    $order['date'],
                    $order['total_amount'],
                    $order['status'],
                ];
            }
        }

        return collect($columns);
    }

    public function startCell(): string
    {
        return 'A1';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'rgb' => 'CCCCCC',
                    ],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ],
        ];
    }
}
