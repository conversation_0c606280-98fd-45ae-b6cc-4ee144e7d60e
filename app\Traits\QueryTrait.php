<?php

namespace App\Traits;

use Illuminate\Support\Facades\Config;

trait QueryTrait {

    public function searchable($query, $request, $fields=[])
    {
        $searchText = $request->input('search');
        if (!empty($searchText)) {
            $query->where(function ($subQuery) use ($fields, $searchText) {
                foreach ($fields as $field) {
                    $subQuery->orWhere($field, 'like', "%$searchText%");
                }
            });
        }
    }

    public function sortable($query, $request)
    {
        if ($request->has('sort_by') && $request->has('sort_direction')) {
            $query->orderBy($request->input('sort_by'), $request->input('sort_direction'));
        }else{
            $query->orderBy('id', 'desc');
        }
    }

    public function searchAndSort($query, $request, $fields=[])
    {
        $this->searchable($query, $request, $fields);
        $this->sortable($query, $request);
    }

    public function getResult($query)
    {
        $perPage = Config::get('constants.perPage');
        return $query->paginate($perPage);
    }

}
