<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('prospects', function (Blueprint $table) {
            // Change timestamp fields to date fields
            $table->date('last_contacted_at')->nullable()->change();
            $table->date('next_follow_up_at')->nullable()->change();
            $table->date('converted_at')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('prospects', function (Blueprint $table) {
            // Revert back to timestamp fields
            $table->timestamp('last_contacted_at')->nullable()->change();
            $table->timestamp('next_follow_up_at')->nullable()->change();
            $table->timestamp('converted_at')->nullable()->change();
        });
    }
};
