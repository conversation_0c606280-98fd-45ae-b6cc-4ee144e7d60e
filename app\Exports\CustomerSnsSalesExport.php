<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomerSnsSalesExport implements FromCollection, WithHeadings, WithTitle, WithCustomStartCell, WithStyles, ShouldAutoSize
{
    protected $allData;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;
    protected $customerName;

    public function __construct(array $allData, $organizationName = null, $customerName = null, $fromDate = null,  $toDate = null)
    {
        $this->allData = $allData;
        $this->organizationName = $organizationName;
        $this->customerName = $customerName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
    }

    public function title(): string
    {
        return 'SNS Customer Sales Report as on ' . now()->format('Y-m-d');
    }

    public function headings(): array
    {
        $headings = [];

        if ($this->organizationName) {
            $headings[] = ["Organization: " . $this->organizationName];
        }

        if ($this->customerName) {
            $headings[] = ["Customer: " . $this->customerName];
        }

        if ($this->fromDate && $this->toDate) {
            $headings[] = ["From Date: " . $this->fromDate . " To Date: " . $this->toDate];
        }

        $headings[] = [
            "Description",  
            "",  
            "",  
            "Qty", 
        ];

        return $headings;
    }

    public function collection()
    {
        $data = $this->allData;
        $rows = [];

        foreach ($data as $customer) {
            $customerName = $customer['customer_name'] ?? 'Unknown Customer';
            $totalQty = 0;

            $rows[] = [
                $customerName,  
                '',  
                '',  
                '',  
            ];

            if (isset($customer['invoices']) && is_array($customer['invoices'])) {
                foreach ($customer['invoices'] as $invoice) {
                    if (isset($invoice['invoice_detail']) && is_array($invoice['invoice_detail'])) {
                        foreach ($invoice['invoice_detail'] as $invoiceDetail) {
                            $productName = $invoiceDetail['product']['name'] ?? 'Unknown Product';
                            $qty = $invoiceDetail['qty'] ?? 0;

                            $rows[] = [
                                ' - ' . $productName, 
                                '',  
                                '',  
                                $qty,  
                            ];
                            $totalQty += $qty;
                        }
                    }
                }
            }

            $rows[] = [
                'Total',  
                '',  
                '',  
                $totalQty,  
            ];
        }

        return collect($rows);
    }

    public function startCell(): string
    {
        return 'A1';
    }

    public function styles(Worksheet $sheet)
    {
        if ($this->organizationName) {
            $sheet->mergeCells('A1:D1');
            $sheet->getStyle('A1:D1')->getFont()->setBold(true)->setSize(12)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
            $sheet->getStyle('A1:D1')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A1:D1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }

        if ($this->customerName) {
            $sheet->mergeCells('A2:D2');
            $sheet->getStyle('A2:D2')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A2:D2')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A2:D2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }

        if ($this->fromDate && $this->toDate) {
            $sheet->mergeCells('A3:D3');
            $sheet->getStyle('A3:D3')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A3:D3')->getAlignment()->setHorizontal('center')->setVertical('center');
            $sheet->getStyle('A3:D3')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        }

        $sheet->mergeCells('A4:C4');  
        $sheet->getStyle('A4:C4')->getFont()->setBold(true)->setSize(10)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
        $sheet->getStyle('A4:C4')->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle('A4:C4')->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle('A4:C4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFCC');

        $sheet->getStyle('D4')->getFont()->setBold(true)->setSize(10)->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_BLACK);
        $sheet->getStyle('D4')->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle('D4')->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle('D4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFCC');

        $highestRow = $sheet->getHighestRow();
        $sheet->getStyle("A5:D$highestRow")->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        $sheet->getStyle("A5:D$highestRow")->getAlignment()->setHorizontal('center')->setVertical('center');
        $sheet->getStyle("A5:D$highestRow")->getFont()->setSize(10);

        foreach (range(5, $highestRow) as $row) {
            $rowValues = $sheet->getCell("A$row")->getValue();

            if (strtolower($rowValues) === 'total') {
                $sheet->getStyle("A$row:D$row")->getFont()->setBold(true);
                $sheet->getStyle("A$row:D$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('D9E1F2');
            } else {
                if ($row % 2 == 0) {
                    $sheet->getStyle("A$row:D$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('F9F9F9');
                } else {
                    $sheet->getStyle("A$row:D$row")->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFFFFF');
                }
            }
        }

        foreach (range('A', 'D') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }
}