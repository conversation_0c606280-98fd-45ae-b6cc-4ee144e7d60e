<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import MultipleFileUpload from '@/Components/MultipleFileUpload.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import { defineProps, ref } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['reporttype', 'serviceperson', 'customer', 'company']);

const form = useForm('post', '/service-reports', {
    customer_id: props.customer.id,
    company_id: '',
    product_code: '',
    product_name:'',
    date: '',
    serial_no: [],
    document: '',
    type:'',
    service_engineer_id:''
});

const serial_no = ref(['']);

const submit = () => {
    form.serial_no = serial_no.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const setReportType = (id, name) => {
    form.type = id;
    form.errors.type = null;
};

const setCompany = (id, name) => {
    form.company_id = id;
    form.errors.company_id = null;
};

const setServicePerson = (id, name) => {
    form.service_engineer_id = id;
    form.errors.service_engineer_id = null;
};

const handleDocument = (file) => {
    form.document = file;
};

const clearError = (fieldName) => {
    form.errors.fieldName = null;
};

const addSerialNoField = () => {
    serial_no.value.push(''); // Add a new empty field
};

const removeSerialNoField = (index) => {
    serial_no.value.splice(index, 1); // Remove the field at the specified index
};

</script>

<template>
    <Head title="Create Report" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Report</h1>
                </div>
                <p class="text-sm font-semibold text-gray-900">{{ customer.customer_name }}</p>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10">
                        <div class="sm:col-span-3">
                            <InputLabel for="product_code" value="Product Code" />
                            <TextInput
                                id="product_code"
                                type="text"
                                @change="clearError('product_code')"
                                v-model="form.product_code"
                                :class="{ 'error rounded-md': form.errors.product_code }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="product_name" value="Product Name" />
                            <TextInput
                                id="product_name"
                                type="text"
                                @change="clearError('product_name')"
                                v-model="form.product_name"
                                :class="{ 'error rounded-md': form.errors.product_name }"
                            />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="company_id" value="Company Name"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="company"
                                    v-model="form.company_id"
                                    @onchange="setCompany"
                                    :class="{ 'error rounded-md': form.errors.company_id }"
                                />
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <InputLabel for="type" value="Report Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="reporttype"
                                    v-model="form.type"
                                    @onchange="setReportType"
                                    :class="{ 'error rounded-md': form.errors.type }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="service_engineer_id" value="Service Engineer"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="serviceperson"
                                    v-model="form.service_engineer_id"
                                    @onchange="setServicePerson"
                                    :class="{ 'error rounded-md': form.errors.service_engineer_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                                <InputLabel for="date" value="Date" />
                                <input
                                    v-model="form.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                    @change="clearError('date')"
                                    :class="{ 'error rounded-md': form.errors.date }"
                                />
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="serial_no" value="Serial No" />
                            <div v-for="(sn, index) in serial_no" :key="index" class="flex items-center gap-x-2 mb-2">
                                <TextInput
                                    :id="`serial_no_${index}`"
                                    type="text"
                                    v-model="serial_no[index]"
                                    :class="{ 'error rounded-md': form.errors.serial_no }"
                                />
                                <div v-if="index != 0">
                                    <button
                                        type="button"
                                        @click="removeSerialNoField(index)"
                                        class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150 ml-3"
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                            <div class="w-32">
                                <button
                                    type="button"
                                    @click="addSerialNoField"
                                    class="flex w-full justify-center rounded-md bg-indigo-600 px-4 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                >
                                    Add Serial No
                                </button>
                            </div>
                        </div>

                        <div class="sm:col-span-4">
                            <div class="w-full">
                            <InputLabel for="note" value="Upload Report" />
                                <MultipleFileUpload
                                    inputId="document"
                                    inputName="document"
                                    @files="handleDocument"
                                />

                                <InputError v-if="form.invalid('document')" :message="form.errors.document"/>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="flex mt-6 items-center justify-between">

                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('service-reports.show' ,{id:customer.id})">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>

                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>


<style scoped>
.error {
    border: 1px solid red;
}
</style>
